"""
日志记录工具模块
用于记录错误和调试信息

日志级别说明：
- ERROR: 写入error.log文件 + 控制台输出
- WARNING: 写入error.log文件 + 控制台输出
- INFO: 仅控制台输出（不写入文件）
- DEBUG: 仅控制台输出（不写入文件）
"""

import os
import sys
import traceback
from datetime import datetime
from .path_utils import get_app_path

class Logger:
    """日志记录器"""
    
    def __init__(self, log_file="error.log"):
        """
        初始化日志记录器
        
        参数:
            log_file (str): 日志文件名
        """
        self.log_file = log_file
        self.log_path = os.path.join(get_app_path(), log_file)
        
        # 确保日志文件目录存在
        log_dir = os.path.dirname(self.log_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    def _write_log(self, level, message, exception=None):
        """
        写入日志
        
        参数:
            level (str): 日志级别
            message (str): 日志消息
            exception (Exception): 异常对象（可选）
        """
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 构建日志条目
            log_entry = f"[{timestamp}] [{level}] {message}\n"
            
            # 如果有异常，添加详细信息
            if exception:
                log_entry += f"异常类型: {type(exception).__name__}\n"
                log_entry += f"异常信息: {str(exception)}\n"
                
                # 添加堆栈跟踪
                if hasattr(exception, '__traceback__') and exception.__traceback__:
                    tb_lines = traceback.format_exception(type(exception), exception, exception.__traceback__)
                    log_entry += "堆栈跟踪:\n"
                    for line in tb_lines:
                        log_entry += f"  {line.rstrip()}\n"
            
            log_entry += "-" * 80 + "\n"
            
            # 写入文件
            with open(self.log_path, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
        except Exception as e:
            # 如果日志写入失败，至少打印到控制台
            print(f"日志写入失败: {e}")
            print(f"原始消息: {message}")
    
    def error(self, message, exception=None):
        """
        记录错误日志
        
        参数:
            message (str): 错误消息
            exception (Exception): 异常对象（可选）
        """
        self._write_log("ERROR", message, exception)
        # 同时打印到控制台
        print(f"[ERROR] {message}")
        if exception:
            print(f"异常: {exception}")
    
    def warning(self, message):
        """
        记录警告日志
        
        参数:
            message (str): 警告消息
        """
        self._write_log("WARNING", message)
        print(f"[WARNING] {message}")
    
    def info(self, message):
        """
        记录信息日志（只在控制台显示，不写入error.log）

        参数:
            message (str): 信息消息
        """
        # INFO级别的日志只在控制台显示，不写入error.log
        print(f"[INFO] {message}")

    def debug(self, message):
        """
        记录调试日志（只在控制台显示，不写入error.log）

        参数:
            message (str): 调试消息
        """
        # DEBUG级别的日志只在控制台显示，不写入error.log
        print(f"[DEBUG] {message}")
    
    def log_segment_error(self, segment_index, segment_text, error_message, exception=None):
        """
        记录分段处理错误的详细信息
        
        参数:
            segment_index (int): 分段索引
            segment_text (str): 分段文本
            error_message (str): 错误消息
            exception (Exception): 异常对象（可选）
        """
        detailed_message = f"分段处理失败 - 分段 {segment_index + 1}\n"
        detailed_message += f"分段文本: {segment_text[:100]}{'...' if len(segment_text) > 100 else ''}\n"
        detailed_message += f"错误信息: {error_message}"
        
        self.error(detailed_message, exception)
    
    def log_tts_error(self, text, voice, rate, pitch, volume, error_message, exception=None):
        """
        记录TTS处理错误的详细信息
        
        参数:
            text (str): 处理的文本
            voice (str): 语音
            rate (str): 语速
            pitch (str): 音调
            volume (str): 音量
            error_message (str): 错误消息
            exception (Exception): 异常对象（可选）
        """
        detailed_message = f"TTS处理失败\n"
        detailed_message += f"文本: {text[:100]}{'...' if len(text) > 100 else ''}\n"
        detailed_message += f"语音: {voice}\n"
        detailed_message += f"语速: {rate}\n"
        detailed_message += f"音调: {pitch}\n"
        detailed_message += f"音量: {volume}\n"
        detailed_message += f"错误信息: {error_message}"
        
        self.error(detailed_message, exception)
    
    def log_audio_processing_error(self, operation, input_file, output_file, error_message, exception=None):
        """
        记录音频处理错误的详细信息
        
        参数:
            operation (str): 操作类型
            input_file (str): 输入文件
            output_file (str): 输出文件
            error_message (str): 错误消息
            exception (Exception): 异常对象（可选）
        """
        detailed_message = f"音频处理失败 - {operation}\n"
        detailed_message += f"输入文件: {input_file}\n"
        detailed_message += f"输出文件: {output_file}\n"
        detailed_message += f"错误信息: {error_message}"
        
        self.error(detailed_message, exception)
    
    def get_log_path(self):
        """
        获取日志文件路径

        返回:
            str: 日志文件的完整路径
        """
        return self.log_path

    def clear_log(self):
        """清空日志文件"""
        try:
            if os.path.exists(self.log_path):
                with open(self.log_path, 'w', encoding='utf-8') as f:
                    f.write(f"日志已清空 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("-" * 80 + "\n")
        except Exception as e:
            print(f"清空日志失败: {e}")
    
    def get_log_path(self):
        """获取日志文件路径"""
        return self.log_path

# 全局日志记录器实例
logger = Logger()
