# 批量命名功能说明

## 功能概述
批量命名功能允许用户对指定文件夹中的文件进行批量重命名操作，支持按文件类型分组重命名，提供预览功能确保重命名结果符合预期。

## 主要特性

### 1. 文件选择
- 支持选择文件夹，自动加载其中的文件
- 支持多种文件类型筛选（音频、视频、图片、文档等）
- 支持自定义扩展名筛选（如：*.mp3;*.srt）

### 2. 操作模式
- **覆盖模式**：直接重命名原文件
- **复制模式**：创建重命名后的文件副本，保留原文件

### 3. 命名规则
- **文件名前缀**：自定义文件名前缀
- **起始序号**：设置编号起始数字
- **数字位数**：控制序号的位数（1-5位，自动补零）

### 4. 文件分组
- 按文件类型自动分组重命名
- 支持的文件类型：
  - 音频文件：.mp3, .wav, .flac, .aac, .m4a, .ogg
  - 视频文件：.mp4, .avi, .mkv, .mov, .wmv, .flv
  - 图片文件：.jpg, .jpeg, .png, .gif, .bmp, .webp
  - 字幕文件：.srt, .ass, .ssa, .vtt, .sub
  - 文档文件：.txt, .doc, .docx, .pdf, .rtf
  - 其他文件：未分类的文件类型

### 5. 预览功能
- 实时预览重命名结果
- 显示原文件名、新文件名、文件类型
- 自动检测命名冲突并标记警告
- 显示文件类型统计信息

### 6. 高级功能
- 右键菜单支持（删除选中、全选、取消选择、刷新预览）
- 自动保存用户设置
- 实时设置验证和错误提示
- 支持删除选中的预览项

## 使用步骤

1. **选择文件夹**
   - 点击"选择文件夹"按钮
   - 选择包含要重命名文件的文件夹

2. **设置筛选条件**
   - 选择文件类型筛选（可选）
   - 输入自定义扩展名（可选）

3. **配置命名规则**
   - 输入文件名前缀
   - 设置起始序号
   - 选择数字位数

4. **预览重命名**
   - 系统自动生成预览
   - 检查重命名结果
   - 注意冲突警告（⚠️标记）

5. **执行重命名**
   - 选择操作模式（覆盖/复制）
   - 点击"执行重命名"按钮
   - 确认操作

## 注意事项

1. **备份重要文件**：建议在重命名前备份重要文件
2. **检查预览**：执行前仔细检查预览结果
3. **解决冲突**：如有命名冲突，调整设置后重新预览
4. **权限问题**：确保对目标文件夹有写入权限
5. **文件占用**：确保要重命名的文件未被其他程序占用

## 错误处理

- 系统会自动验证设置参数
- 显示详细的错误信息和状态
- 跳过无法处理的文件，继续处理其他文件
- 显示成功处理的文件数量

## 设置保存

用户的设置会自动保存到 `config/batch_rename_settings.json` 文件中，下次使用时会自动恢复上次的设置。
