import os
import shutil
import re
from pathlib import Path

class BatchRenameProcessor:
    def __init__(self):
        pass

    def natural_sort_key(self, filename):
        """
        生成自然排序的键，让数字按数值大小排序
        例如：file_1.txt, file_2.txt, file_10.txt 而不是 file_1.txt, file_10.txt, file_2.txt
        """
        def convert(text):
            return int(text) if text.isdigit() else text.lower()

        # 将文件名分割为数字和非数字部分
        return [convert(c) for c in re.split(r'(\d+)', filename)]
    
    def get_files_in_folder(self, folder_path, file_filter=None):
        """获取文件夹中的文件列表"""
        files = []
        
        try:
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                
                # 只处理文件，不处理文件夹
                if os.path.isfile(item_path):
                    # 应用文件过滤器
                    if self._should_include_file(item, file_filter):
                        files.append({
                            "path": item_path,
                            "name": item,
                            "extension": os.path.splitext(item)[1].lower(),
                            "size": os.path.getsize(item_path)
                        })
        
        except Exception as e:
            raise Exception(f"读取文件夹失败: {str(e)}")
        
        # 按文件名自然排序（数字按数值大小排序）
        files.sort(key=lambda x: self.natural_sort_key(x["name"]))
        return files
    
    def _should_include_file(self, filename, file_filter):
        """判断文件是否应该包含在列表中"""
        if not file_filter:
            return True

        file_ext = os.path.splitext(filename)[1].lower()

        # 处理自定义扩展名格式（可能包含*号）
        normalized_filter = []
        for ext in file_filter:
            if ext.startswith("*."):
                normalized_filter.append(ext[1:])  # 移除*号
            elif ext.startswith("."):
                normalized_filter.append(ext)
            else:
                normalized_filter.append("." + ext)  # 添加点号

        return file_ext in normalized_filter
    
    def generate_rename_preview(self, file_list, prefix, start_num, digits, custom_extensions=None, separator="_"):
        """
        生成重命名预览 - 按扩展名分组，同数字对应同后缀

        参数:
            file_list: 文件列表
            prefix: 文件名前缀
            start_num: 起始序号
            digits: 数字位数
            custom_extensions: 自定义扩展名列表，如 ['.mp3', '.srt']
            separator: 连接符，默认为下划线
        """
        preview = []

        if not custom_extensions:
            # 如果没有指定自定义扩展名，使用原来的逻辑
            return self._generate_simple_rename_preview(file_list, prefix, start_num, digits, separator)

        # 按扩展名分组文件
        extension_groups = {}
        for file_info in file_list:
            ext = file_info['extension'].lower()
            if ext in custom_extensions:  # 只处理指定的扩展名
                if ext not in extension_groups:
                    extension_groups[ext] = []
                extension_groups[ext].append(file_info)

        # 按扩展名顺序排序（按照用户指定的顺序）
        for ext in custom_extensions:
            if ext in extension_groups:
                # 按文件名自然排序（数字按数值大小排序）
                extension_groups[ext].sort(key=lambda x: self.natural_sort_key(x["name"]))

        # 找出最大的文件数量（用于确定需要多少个数字）
        max_count = max(len(files) for files in extension_groups.values()) if extension_groups else 0

        # 为每个数字生成对应的文件名
        for i in range(max_count):
            current_num = start_num + i
            number_str = str(current_num).zfill(digits)

            # 为每个扩展名生成对应的文件
            for ext in custom_extensions:
                if ext in extension_groups and i < len(extension_groups[ext]):
                    file_info = extension_groups[ext][i]

                    # 生成新文件名
                    if prefix.strip():
                        # 有前缀时使用连接符连接：prefix{separator}001.ext
                        if separator:
                            new_name = f"{prefix}{separator}{number_str}{ext}"
                        else:
                            new_name = f"{prefix}{number_str}{ext}"
                    else:
                        # 无前缀时直接使用序号：001.ext
                        new_name = f"{number_str}{ext}"

                    # 获取文件类型（用于显示）
                    file_type = self._get_file_type(ext)

                    preview.append({
                        "original_path": file_info["path"],
                        "original_name": file_info["name"],
                        "new_name": new_name,
                        "file_type": file_type,
                        "extension": ext,
                        "group_number": current_num  # 添加组号用于显示
                    })

        return preview

    def _generate_simple_rename_preview(self, file_list, prefix, start_num, digits, separator="_"):
        """生成简单重命名预览（原来的逻辑）"""
        preview = []
        current_num = start_num

        for file_info in file_list:
            # 生成新文件名
            number_str = str(current_num).zfill(digits)

            if prefix.strip():
                # 有前缀时使用连接符连接：prefix{separator}001.ext
                if separator:
                    new_name = f"{prefix}{separator}{number_str}{file_info['extension']}"
                else:
                    new_name = f"{prefix}{number_str}{file_info['extension']}"
            else:
                # 无前缀时直接使用序号：001.ext
                new_name = f"{number_str}{file_info['extension']}"

            # 获取文件类型（用于显示）
            file_type = self._get_file_type(file_info['extension'])

            preview.append({
                "original_path": file_info["path"],
                "original_name": file_info["name"],
                "new_name": new_name,
                "file_type": file_type,
                "extension": file_info["extension"]
            })

            current_num += 1

        return preview
    
    def _get_file_type(self, extension):
        """获取文件类型"""
        type_mapping = {
            # 音频文件
            ".mp3": "音频文件", ".wav": "音频文件", ".flac": "音频文件",
            ".aac": "音频文件", ".m4a": "音频文件", ".ogg": "音频文件",

            # 视频文件
            ".mp4": "视频文件", ".avi": "视频文件", ".mkv": "视频文件",
            ".mov": "视频文件", ".wmv": "视频文件", ".flv": "视频文件",

            # 图片文件
            ".jpg": "图片文件", ".jpeg": "图片文件", ".png": "图片文件",
            ".gif": "图片文件", ".bmp": "图片文件", ".webp": "图片文件",

            # 字幕文件
            ".srt": "字幕文件", ".ass": "字幕文件", ".ssa": "字幕文件",
            ".vtt": "字幕文件", ".sub": "字幕文件",

            # 文档文件
            ".txt": "文档文件", ".doc": "文档文件", ".docx": "文档文件",
            ".pdf": "文档文件", ".rtf": "文档文件"
        }

        return type_mapping.get(extension, "其他文件")

    def _group_files_by_type(self, file_list):
        """按文件类型分组（用于统计）"""
        groups = {}

        for file_info in file_list:
            file_type = self._get_file_type(file_info["extension"])
            if file_type not in groups:
                groups[file_type] = []
            groups[file_type].append(file_info)

        return groups
    
    def execute_rename(self, rename_preview, copy_mode=False):
        """执行重命名操作"""
        success_count = 0
        
        for item in rename_preview:
            try:
                original_path = item["original_path"]
                folder_path = os.path.dirname(original_path)
                new_path = os.path.join(folder_path, item["new_name"])
                
                if copy_mode:
                    # 复制模式
                    if not os.path.exists(new_path):
                        shutil.copy2(original_path, new_path)
                        success_count += 1
                else:
                    # 覆盖模式（重命名）
                    if not os.path.exists(new_path) or new_path != original_path:
                        os.rename(original_path, new_path)
                        success_count += 1
                        
            except Exception as e:
                # 记录错误但继续处理其他文件
                print(f"处理文件 {item['original_name']} 失败: {str(e)}")
                continue
        
        return success_count

    def test_grouped_rename(self):
        """测试分组重命名功能"""
        # 创建测试文件列表
        test_files = [
            {"path": "/test/audio1.mp3", "name": "audio1.mp3", "extension": ".mp3"},
            {"path": "/test/subtitle1.srt", "name": "subtitle1.srt", "extension": ".srt"},
            {"path": "/test/audio2.mp3", "name": "audio2.mp3", "extension": ".mp3"},
            {"path": "/test/subtitle2.srt", "name": "subtitle2.srt", "extension": ".srt"},
            {"path": "/test/audio3.mp3", "name": "audio3.mp3", "extension": ".mp3"},
        ]

        # 测试分组重命名
        custom_extensions = [".mp3", ".srt"]
        preview = self.generate_rename_preview(test_files, "", 1, 1, custom_extensions)

        print("测试分组重命名结果：")
        for item in preview:
            print(f"  {item['original_name']} -> {item['new_name']} (组号: {item.get('group_number', 'N/A')})")

        return preview

    def test_natural_sorting(self):
        """测试自然排序功能"""
        # 创建测试文件列表（故意打乱顺序）
        test_files = [
            {"path": "/test/file_10.txt", "name": "file_10.txt", "extension": ".txt"},
            {"path": "/test/file_2.txt", "name": "file_2.txt", "extension": ".txt"},
            {"path": "/test/file_1.txt", "name": "file_1.txt", "extension": ".txt"},
            {"path": "/test/file_20.txt", "name": "file_20.txt", "extension": ".txt"},
            {"path": "/test/file_3.txt", "name": "file_3.txt", "extension": ".txt"},
            {"path": "/test/audio_1.mp3", "name": "audio_1.mp3", "extension": ".mp3"},
            {"path": "/test/audio_10.mp3", "name": "audio_10.mp3", "extension": ".mp3"},
            {"path": "/test/audio_2.mp3", "name": "audio_2.mp3", "extension": ".mp3"},
        ]

        print("排序前的文件顺序：")
        for file_info in test_files:
            print(f"  {file_info['name']}")

        # 使用自然排序
        test_files.sort(key=lambda x: self.natural_sort_key(x["name"]))

        print("\n自然排序后的文件顺序：")
        for file_info in test_files:
            print(f"  {file_info['name']}")

        return test_files
    
    def validate_rename_settings(self, prefix, start_num, digits):
        """验证重命名设置"""
        errors = []

        # 前缀可以为空，不需要验证

        # 验证起始序号
        try:
            num = int(start_num)
            if num < 0:
                errors.append("起始序号不能为负数")
        except ValueError:
            errors.append("起始序号必须是数字")

        # 验证数字位数
        try:
            digits_num = int(digits)
            if digits_num < 1 or digits_num > 10:
                errors.append("数字位数必须在1-10之间")
        except ValueError:
            errors.append("数字位数必须是数字")

        return errors
    
    def check_name_conflicts(self, rename_preview):
        """检查重命名冲突"""
        conflicts = []
        new_names = set()
        
        for item in rename_preview:
            new_name = item["new_name"]
            if new_name in new_names:
                conflicts.append(f"重复的新文件名: {new_name}")
            else:
                new_names.add(new_name)
        
        return conflicts
    
    def get_file_type_stats(self, file_list):
        """获取文件类型统计"""
        stats = {}
        groups = self._group_files_by_type(file_list)
        
        for group_name, files in groups.items():
            stats[group_name] = len(files)
        
        return stats
