import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import shutil
import threading
import json
from .core import BatchRenameProcessor

# 添加common模块路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from common.path_utils import get_config_dir_path, ensure_dir_exists

class BatchRenameWindow:
    def __init__(self, parent):
        self.parent = parent
        self.processor = BatchRenameProcessor()
        
        # 设置颜色主题
        self.bg_color = "#f8f9fa"
        self.panel_color = "#ffffff"
        self.text_color = "#212529"
        self.accent_color = "#4361ee"
        self.button_text_color = "#ffffff"
        
        # 创建主框架
        self.frame = tk.Frame(parent, bg=self.bg_color, padx=15, pady=10)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件列表
        self.file_list = []
        self.rename_preview = []
        
        # 创建界面
        self.create_top_section()
        self.create_settings_section()
        self.create_preview_section()
        self.create_bottom_section()

        # 加载用户设置
        self.load_settings()
    
    def create_top_section(self):
        """创建顶部区域，包含标题和文件选择"""
        top_frame = tk.Frame(self.frame, bg=self.bg_color)
        top_frame.pack(fill=tk.X, pady=10)
        
        # 标题
        title_label = tk.Label(
            top_frame,
            text="批量命名",
            font=("微软雅黑", 16, "bold"),
            fg=self.text_color,
            bg=self.bg_color
        )
        title_label.pack(side=tk.LEFT, padx=10)
        
        # 文件选择区域
        file_frame = tk.Frame(self.frame, bg=self.bg_color)
        file_frame.pack(fill=tk.X, pady=10)
        
        # 文件路径标签
        path_label = tk.Label(
            file_frame,
            text="选择要重命名的文件夹:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.bg_color
        )
        path_label.pack(side=tk.LEFT, padx=(0, 10))
        
        # 文件路径输入框
        self.path_var = tk.StringVar()
        self.path_entry = tk.Entry(
            file_frame,
            textvariable=self.path_var,
            font=("微软雅黑", 10),
            width=50
        )
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 选择文件夹按钮
        select_btn = tk.Button(
            file_frame,
            text="选择文件夹",
            font=("微软雅黑", 10),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=15,
            pady=5,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.select_folder
        )
        select_btn.pack(side=tk.RIGHT)
    
    def create_settings_section(self):
        """创建设置区域"""
        settings_frame = tk.Frame(self.frame, bg=self.panel_color, relief=tk.RAISED, bd=1)
        settings_frame.pack(fill=tk.X, pady=10)
        
        inner_frame = tk.Frame(settings_frame, bg=self.panel_color, padx=15, pady=15)
        inner_frame.pack(fill=tk.X)
        
        # 操作模式
        mode_frame = tk.Frame(inner_frame, bg=self.panel_color)
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        mode_label = tk.Label(
            mode_frame,
            text="操作模式:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        mode_label.pack(side=tk.LEFT)
        
        self.mode_var = tk.StringVar(value="覆盖模式")
        mode_radio1 = tk.Radiobutton(
            mode_frame,
            text="覆盖模式",
            variable=self.mode_var,
            value="覆盖模式",
            font=("微软雅黑", 10),
            bg=self.panel_color,
            fg=self.text_color
        )
        mode_radio1.pack(side=tk.LEFT, padx=(20, 10))
        
        mode_radio2 = tk.Radiobutton(
            mode_frame,
            text="复制模式",
            variable=self.mode_var,
            value="复制模式",
            font=("微软雅黑", 10),
            bg=self.panel_color,
            fg=self.text_color
        )
        mode_radio2.pack(side=tk.LEFT)
        
        # 命名规则说明
        rule_frame = tk.Frame(inner_frame, bg=self.panel_color)
        rule_frame.pack(fill=tk.X, pady=(0, 10))

        rule_label = tk.Label(
            rule_frame,
            text="命名规则:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        rule_label.pack(side=tk.LEFT)

        self.rule_desc = tk.Label(
            rule_frame,
            text="有前缀: 前缀_001.ext  |  无前缀: 001.ext",
            font=("微软雅黑", 9),
            fg="#666666",
            bg=self.panel_color
        )
        self.rule_desc.pack(side=tk.LEFT, padx=(10, 0))
        
        # 命名设置行
        naming_frame = tk.Frame(inner_frame, bg=self.panel_color)
        naming_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件名前缀
        prefix_label = tk.Label(
            naming_frame,
            text="文件名前缀:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        prefix_label.grid(row=0, column=0, sticky="w", padx=(0, 10))
        
        self.prefix_var = tk.StringVar()
        prefix_entry = tk.Entry(
            naming_frame,
            textvariable=self.prefix_var,
            font=("微软雅黑", 10),
            width=15
        )
        prefix_entry.grid(row=0, column=1, padx=(0, 10))

        # 连接符
        separator_label = tk.Label(
            naming_frame,
            text="连接符:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        separator_label.grid(row=0, column=2, sticky="w", padx=(0, 10))

        self.separator_var = tk.StringVar(value="_")
        separator_entry = tk.Entry(
            naming_frame,
            textvariable=self.separator_var,
            font=("微软雅黑", 10),
            width=5
        )
        separator_entry.grid(row=0, column=3, padx=(0, 20))

        # 起始序号
        start_label = tk.Label(
            naming_frame,
            text="起始序号:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        start_label.grid(row=0, column=4, sticky="w", padx=(0, 10))
        
        self.start_num_var = tk.StringVar(value="1")
        start_entry = tk.Entry(
            naming_frame,
            textvariable=self.start_num_var,
            font=("微软雅黑", 10),
            width=10
        )
        start_entry.grid(row=0, column=5, padx=(0, 20))

        # 数字位数
        digits_label = tk.Label(
            naming_frame,
            text="数字位数:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        digits_label.grid(row=0, column=6, sticky="w", padx=(0, 10))

        self.digits_var = tk.StringVar(value="1")
        digits_combo = ttk.Combobox(
            naming_frame,
            textvariable=self.digits_var,
            values=["1", "2", "3", "4", "5"],
            width=8,
            state="readonly"
        )
        digits_combo.grid(row=0, column=7)
        
        # 文件类型筛选行
        filter_frame = tk.Frame(inner_frame, bg=self.panel_color)
        filter_frame.pack(fill=tk.X)
        
        filter_label = tk.Label(
            filter_frame,
            text="文件类型筛选:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        filter_label.grid(row=0, column=0, sticky="w", padx=(0, 10))
        
        self.filter_var = tk.StringVar(value="所有文件")
        filter_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.filter_var,
            values=["所有文件", "音频文件", "视频文件", "图片文件", "文档文件"],
            width=15,
            state="readonly"
        )
        filter_combo.grid(row=0, column=1, padx=(0, 20))
        
        # 自定义扩展名
        ext_label = tk.Label(
            filter_frame,
            text="自定义扩展名:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        ext_label.grid(row=0, column=2, sticky="w", padx=(20, 10))

        self.ext_var = tk.StringVar(value="mp3;srt")
        ext_entry = tk.Entry(
            filter_frame,
            textvariable=self.ext_var,
            font=("微软雅黑", 10),
            width=20
        )
        ext_entry.grid(row=0, column=3, padx=(0, 0))

        # 添加提示标签
        hint_label = tk.Label(
            filter_frame,
            text="(用分号分隔多个扩展名，如: mp3;srt)",
            font=("微软雅黑", 8),
            fg="#666666",
            bg=self.panel_color
        )
        hint_label.grid(row=1, column=3, sticky="w", pady=(2, 0))

        # 添加命名规则说明
        rule_label = tk.Label(
            filter_frame,
            text="📝 命名规则：同数字对应同后缀，如 1.mp3 + 1.srt, 2.mp3 + 2.srt",
            font=("微软雅黑", 8),
            fg="#007bff",
            bg=self.panel_color
        )
        rule_label.grid(row=2, column=0, columnspan=4, sticky="w", pady=(5, 0))
        
        # 绑定事件
        filter_combo.bind("<<ComboboxSelected>>", self.on_filter_change)
        self.prefix_var.trace("w", lambda *args: self.on_settings_change())
        self.separator_var.trace("w", lambda *args: self.on_settings_change())
        self.start_num_var.trace("w", lambda *args: self.on_settings_change())
        self.digits_var.trace("w", lambda *args: self.on_settings_change())
        self.ext_var.trace("w", lambda *args: self.on_filter_change(None))
        
        # 预览和执行按钮
        button_frame = tk.Frame(inner_frame, bg=self.panel_color)
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        # 清空列表按钮
        clear_btn = tk.Button(
            button_frame,
            text="清空列表",
            font=("微软雅黑", 10),
            bg="#6c757d",
            fg=self.button_text_color,
            padx=15,
            pady=5,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.clear_list
        )
        clear_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 删除选中按钮
        delete_btn = tk.Button(
            button_frame,
            text="删除选中",
            font=("微软雅黑", 10),
            bg="#dc3545",
            fg=self.button_text_color,
            padx=15,
            pady=5,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.delete_selected
        )
        delete_btn.pack(side=tk.RIGHT, padx=(10, 0))
    
    def create_preview_section(self):
        """创建预览区域"""
        preview_frame = tk.Frame(self.frame, bg=self.bg_color)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 标题
        title_label = tk.Label(
            preview_frame,
            text="重命名预览:",
            font=("微软雅黑", 12, "bold"),
            fg=self.text_color,
            bg=self.bg_color
        )
        title_label.pack(anchor="w", pady=(0, 10))
        
        # 创建表格
        columns = ("原文件名", "新文件名", "文件类型", "组号")
        self.tree = ttk.Treeview(preview_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        column_widths = {"原文件名": 250, "新文件名": 200, "文件类型": 100, "组号": 80}
        for col in columns:
            self.tree.heading(col, text=col)
            width = column_widths.get(col, 200)
            self.tree.column(col, width=width, anchor="w")
        
        # 滚动条
        scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建右键菜单
        self.create_context_menu()

    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="删除选中项", command=self.delete_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="全选", command=self.select_all)
        self.context_menu.add_command(label="取消选择", command=self.deselect_all)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="刷新预览", command=self.preview_rename)

        # 绑定右键事件
        self.tree.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def select_all(self):
        """全选"""
        for item in self.tree.get_children():
            self.tree.selection_add(item)

    def deselect_all(self):
        """取消选择"""
        self.tree.selection_remove(self.tree.selection())
    
    def create_bottom_section(self):
        """创建底部按钮区域"""
        bottom_frame = tk.Frame(self.frame, bg=self.bg_color)
        bottom_frame.pack(fill=tk.X, pady=10)
        
        # 预览重命名按钮
        preview_btn = tk.Button(
            bottom_frame,
            text="预览重命名",
            font=("微软雅黑", 12),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=20,
            pady=8,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.preview_rename
        )
        preview_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 执行重命名按钮
        execute_btn = tk.Button(
            bottom_frame,
            text="执行重命名",
            font=("微软雅黑", 12),
            bg="#28a745",
            fg=self.button_text_color,
            padx=20,
            pady=8,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.execute_rename
        )
        execute_btn.pack(side=tk.LEFT)
        
        # 状态标签
        self.status_label = tk.Label(
            bottom_frame,
            text="就绪",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.bg_color
        )
        self.status_label.pack(side=tk.RIGHT)

        # 文件统计标签
        self.stats_label = tk.Label(
            bottom_frame,
            text="",
            font=("微软雅黑", 9),
            fg="#666666",
            bg=self.bg_color
        )
        self.stats_label.pack(side=tk.RIGHT, padx=(0, 20))
    
    def select_folder(self):
        """选择文件夹"""
        folder_path = filedialog.askdirectory(title="选择要重命名文件的文件夹")
        if folder_path:
            self.path_var.set(folder_path)
            self.load_files()
    
    def load_files(self):
        """加载文件列表"""
        folder_path = self.path_var.get()
        if not folder_path or not os.path.exists(folder_path):
            return
        
        try:
            # 获取文件列表
            self.file_list = self.processor.get_files_in_folder(
                folder_path, 
                self.get_file_filter()
            )
            
            # 更新状态和统计
            self.status_label.config(text=f"已加载 {len(self.file_list)} 个文件")
            self.update_file_stats()

            # 自动预览
            self.preview_rename()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载文件失败: {str(e)}")
    
    def get_file_filter(self):
        """获取文件过滤器"""
        filter_type = self.filter_var.get()
        custom_ext = self.ext_var.get().strip()

        if filter_type != "所有文件" and custom_ext:
            # 使用自定义扩展名
            extensions = []
            for ext in custom_ext.split(";"):
                ext = ext.strip().lower()
                if ext:
                    # 确保扩展名以点开头
                    if not ext.startswith('.'):
                        ext = '.' + ext
                    # 移除通配符
                    ext = ext.replace('*', '')
                    extensions.append(ext)
            return extensions if extensions else None

        # 预定义过滤器
        filters = {
            "所有文件": None,
            "音频文件": [".mp3", ".wav", ".flac", ".aac", ".m4a", ".ogg"],
            "视频文件": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv"],
            "图片文件": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"],
            "文档文件": [".txt", ".doc", ".docx", ".pdf", ".srt", ".ass"]
        }

        return filters.get(filter_type)

    def get_custom_extensions_for_rename(self):
        """获取用于重命名的自定义扩展名列表（保持用户指定的顺序）"""
        custom_ext = self.ext_var.get().strip()
        if not custom_ext:
            return None

        extensions = []
        for ext in custom_ext.split(";"):
            ext = ext.strip().lower()
            if ext:
                # 确保扩展名以点开头
                if not ext.startswith('.'):
                    ext = '.' + ext
                # 移除通配符
                ext = ext.replace('*', '')
                extensions.append(ext)

        return extensions if extensions else None
    
    def on_filter_change(self, event=None):
        """文件类型筛选改变时的处理"""
        self.load_files()

    def on_settings_change(self):
        """设置改变时自动更新预览"""
        # 更新规则描述
        self.update_rule_description()

        if self.file_list:
            self.preview_rename()
        # 保存设置
        self.save_settings()

    def update_rule_description(self):
        """更新命名规则描述"""
        separator = self.separator_var.get()
        if separator:
            rule_text = f"有前缀: 前缀{separator}001.ext  |  无前缀: 001.ext"
        else:
            rule_text = "有前缀: 前缀001.ext  |  无前缀: 001.ext"
        self.rule_desc.config(text=rule_text)
    
    def preview_rename(self):
        """预览重命名"""
        if not self.file_list:
            if hasattr(self, 'tree'):
                # 清空表格
                for item in self.tree.get_children():
                    self.tree.delete(item)
            return

        try:
            # 获取重命名设置
            prefix = self.prefix_var.get()
            separator = self.separator_var.get()
            start_num = int(self.start_num_var.get() or "1")
            digits = int(self.digits_var.get())

            # 验证设置
            errors = self.processor.validate_rename_settings(prefix, start_num, digits)
            if errors:
                self.status_label.config(text=f"设置错误: {'; '.join(errors)}")
                return

            # 获取自定义扩展名（用于重命名分组）
            custom_extensions = self.get_custom_extensions_for_rename()

            # 生成预览
            self.rename_preview = self.processor.generate_rename_preview(
                self.file_list, prefix, start_num, digits, custom_extensions, separator
            )

            # 检查冲突
            conflicts = self.processor.check_name_conflicts(self.rename_preview)
            if conflicts:
                self.status_label.config(text=f"命名冲突: {conflicts[0]}")
            else:
                self.status_label.config(text=f"预览完成，共 {len(self.rename_preview)} 个文件")

            # 更新表格
            self.update_preview_table()

        except ValueError:
            self.status_label.config(text="起始序号必须是数字")
        except Exception as e:
            self.status_label.config(text=f"预览失败: {str(e)}")
    
    def update_preview_table(self):
        """更新预览表格"""
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 检查重名冲突
        name_counts = {}
        for item in self.rename_preview:
            new_name = item["new_name"]
            name_counts[new_name] = name_counts.get(new_name, 0) + 1

        # 添加预览数据，标记冲突项
        for item in self.rename_preview:
            new_name = item["new_name"]
            # 如果有重名冲突，在新文件名后添加警告标记
            display_name = new_name
            if name_counts[new_name] > 1:
                display_name = f"{new_name} ⚠️"

            # 获取组号（如果有的话）
            group_number = item.get("group_number", "")

            tree_item = self.tree.insert("", "end", values=(
                item["original_name"],
                display_name,
                item["file_type"],
                group_number
            ))

            # 为冲突项设置不同的背景色
            if name_counts[new_name] > 1:
                self.tree.set(tree_item, "新文件名", display_name)
    
    def execute_rename(self):
        """执行重命名"""
        if not self.rename_preview:
            messagebox.showwarning("警告", "请先预览重命名")
            return
        
        # 确认对话框
        if not messagebox.askyesno("确认", "确定要执行重命名操作吗？"):
            return
        
        # 在后台线程中执行
        threading.Thread(target=self._execute_rename_thread, daemon=True).start()
    
    def _execute_rename_thread(self):
        """在后台线程中执行重命名"""
        try:
            self.status_label.config(text="正在重命名...")
            
            mode = self.mode_var.get()
            success_count = self.processor.execute_rename(
                self.rename_preview, 
                mode == "复制模式"
            )
            
            self.status_label.config(text=f"重命名完成，成功处理 {success_count} 个文件")
            messagebox.showinfo("完成", f"重命名完成！成功处理 {success_count} 个文件")
            
            # 重新加载文件列表
            self.load_files()
            
        except Exception as e:
            self.status_label.config(text="重命名失败")
            messagebox.showerror("错误", f"重命名失败: {str(e)}")
    
    def clear_list(self):
        """清空列表"""
        self.file_list = []
        self.rename_preview = []
        self.update_preview_table()
        self.status_label.config(text="已清空列表")
    
    def delete_selected(self):
        """删除选中项"""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择要删除的项目")
            return
        
        # 获取选中项的索引
        indices_to_remove = []
        for item in selected_items:
            index = self.tree.index(item)
            indices_to_remove.append(index)
        
        # 从后往前删除，避免索引变化
        for index in sorted(indices_to_remove, reverse=True):
            if index < len(self.rename_preview):
                del self.rename_preview[index]
        
        # 更新表格
        self.update_preview_table()
        self.status_label.config(text=f"已删除 {len(indices_to_remove)} 个项目")

    def update_file_stats(self):
        """更新文件统计信息"""
        if not self.file_list:
            self.stats_label.config(text="")
            return

        stats = self.processor.get_file_type_stats(self.file_list)
        stats_text = " | ".join([f"{k}: {v}" for k, v in stats.items() if v > 0])
        self.stats_label.config(text=stats_text)

    def load_settings(self):
        """加载用户设置"""
        try:
            config_dir = get_config_dir_path()
            settings_file = os.path.join(config_dir, "batch_rename_settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # 恢复设置
                self.prefix_var.set(settings.get("prefix", ""))
                self.separator_var.set(settings.get("separator", "_"))
                self.start_num_var.set(settings.get("start_num", "1"))
                self.digits_var.set(settings.get("digits", "1"))
                self.mode_var.set(settings.get("mode", "覆盖模式"))
                self.filter_var.set(settings.get("filter", "所有文件"))
                self.ext_var.set(settings.get("custom_ext", "mp3;srt"))

                # 更新规则描述
                self.update_rule_description()

        except Exception as e:
            print(f"加载设置失败: {str(e)}")

    def save_settings(self):
        """保存用户设置"""
        try:
            # 确保配置目录存在
            config_dir = get_config_dir_path()
            ensure_dir_exists(config_dir)

            settings = {
                "prefix": self.prefix_var.get(),
                "separator": self.separator_var.get(),
                "start_num": self.start_num_var.get(),
                "digits": self.digits_var.get(),
                "mode": self.mode_var.get(),
                "filter": self.filter_var.get(),
                "custom_ext": self.ext_var.get()
            }

            settings_file = os.path.join(config_dir, "batch_rename_settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"保存设置失败: {str(e)}")
