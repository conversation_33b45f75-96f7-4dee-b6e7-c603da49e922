#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频合成循环视频编码优化
"""

import os
import sys
import tempfile
import time

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.video_composer.core import VideoComposerProcessor

def test_optimization():
    """测试循环视频编码优化"""
    
    print("🧪 开始测试视频合成循环视频编码优化...")
    
    # 创建处理器实例
    processor = VideoComposerProcessor()
    
    if not processor.is_available():
        print("❌ FFmpeg不可用，无法进行测试")
        return False
    
    print("✅ FFmpeg可用，开始测试")
    
    # 使用现有的测试视频文件
    test_video_path = "data/微信视频2025-07-20_022016_851.mp4"

    if not os.path.exists(test_video_path):
        print(f"⚠️ 测试视频文件不存在: {test_video_path}")
        print("请提供一个测试视频文件来验证优化效果")
        return False
    
    # 模拟不同时长的音频文件组
    duration_groups = {
        30.0: ["audio1.mp3", "audio2.mp3"],  # 30秒的音频
        45.0: ["audio3.mp3"],                # 45秒的音频
        60.0: ["audio4.mp3", "audio5.mp3"],  # 60秒的音频
    }
    
    # 测试设置 - 不启用编码，测试优化后的简单拼接
    settings = {
        'custom_resolution': False,  # 不自定义分辨率
        'custom_compression': False,  # 不自定义压缩
        'enable_parallel': False,  # 先测试串行模式
        'max_threads': 2
    }
    
    def progress_callback(message):
        print(f"📝 {message}")
    
    print("\n🚀 开始测试优化后的批量循环视频生成...")
    start_time = time.time()
    
    try:
        # 测试批量生成循环视频
        loop_videos = processor.batch_generate_loop_videos(
            duration_groups, 
            test_video_path, 
            settings, 
            progress_callback
        )
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"\n⏱️ 总耗时: {elapsed_time:.2f} 秒")
        print(f"📊 生成结果:")
        
        success_count = 0
        for duration, video_path in loop_videos.items():
            if video_path and os.path.exists(video_path):
                success_count += 1
                print(f"  ✅ {duration:.1f}秒: {video_path}")
            else:
                print(f"  ❌ {duration:.1f}秒: 生成失败")
        
        print(f"\n📈 成功率: {success_count}/{len(duration_groups)} ({success_count/len(duration_groups)*100:.1f}%)")
        
        if success_count > 0:
            avg_time = elapsed_time / success_count
            print(f"📊 平均每个循环视频生成时间: {avg_time:.2f} 秒")
            
            print("\n🎯 优化效果分析:")
            print("- 如果看到 '预编码' 相关日志，说明优化生效")
            print("- 如果看到 '快速循环' 相关日志，说明使用了预编码视频")
            print("- 预期效果：第一个视频编码时间较长，后续视频生成很快")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")
        return False

def test_settings_detection():
    """测试设置检测逻辑"""
    print("\n🔍 测试设置检测逻辑...")
    
    processor = VideoComposerProcessor()
    
    # 测试不需要编码的设置
    settings1 = {}
    result1 = processor._should_use_pre_encoding(settings1)
    print(f"空设置 → 需要预编码: {result1} (预期: False)")
    
    # 测试需要编码的设置
    settings2 = {'custom_resolution': True, 'width': 1280, 'height': 720}
    result2 = processor._should_use_pre_encoding(settings2)
    print(f"自定义分辨率 → 需要预编码: {result2} (预期: True)")
    
    settings3 = {'custom_compression': True, 'current_compression_bitrate': 2000}
    result3 = processor._should_use_pre_encoding(settings3)
    print(f"自定义压缩 → 需要预编码: {result3} (预期: True)")
    
    settings4 = {'force_encoding': True}
    result4 = processor._should_use_pre_encoding(settings4)
    print(f"强制编码 → 需要预编码: {result4} (预期: True)")
    
    return all([not result1, result2, result3, result4])

if __name__ == "__main__":
    print("=" * 60)
    print("视频合成循环视频编码优化测试")
    print("=" * 60)
    
    # 测试设置检测
    settings_ok = test_settings_detection()
    print(f"\n设置检测测试: {'✅ 通过' if settings_ok else '❌ 失败'}")
    
    # 测试优化功能
    if settings_ok:
        optimization_ok = test_optimization()
        print(f"\n优化功能测试: {'✅ 通过' if optimization_ok else '❌ 失败'}")
    else:
        print("\n⚠️ 设置检测测试失败，跳过优化功能测试")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
