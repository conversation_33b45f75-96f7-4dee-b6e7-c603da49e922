"""
配置管理模块
用于保存和加载用户设置
"""

import os
import sys
import json
from typing import Dict, Any

# 添加common模块路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from common.path_utils import get_config_path, ensure_dir_exists

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file="config.json"):
        """
        初始化配置管理器

        参数:
            config_file (str): 配置文件名
        """
        self.config_file = config_file
        # 使用新的路径获取函数，兼容打包后的环境
        self.config_path = get_config_path(config_file)
        self.default_config = {
            "ffmpeg_path": "",
            "last_output_dir": "",
            "voice_settings": {
                "voice": "zh-CN-XiaoxiaoNeural",
                "rate": 0,
                "pitch": 0,
                "volume": 50  # 支持0-300的音量范围
            },
            "generate_srt": True,
            "window_settings": {
                "width": 1000,
                "height": 700
            },
            "advanced_settings": {
                "segment_length": 3000,  # 分块大小 (1000-5000) - 增加默认值以减少停顿
                "thread_count": 5,       # 线程数 (1-20) - 优化多线程性能
                "retry_count": 3,        # 重试次数
                "retry_interval": 2,     # 重试间隔时间(秒)
                # 停顿移除设置已移除
                "enhance_quality": True, # 是否增强音频质量（保持兼容性）
                "enhance_clarity": False, # 是否增强清晰度（默认关闭以提高速度）
                "normalize_volume": False # 是否标准化音量（已移除）
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        返回:
            dict: 配置字典
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 合并默认配置，确保所有必要的键都存在
                merged_config = self.default_config.copy()
                self._merge_config(merged_config, config)
                return merged_config
            else:
                # 配置文件不存在，返回默认配置
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        返回:
            bool: 是否保存成功
        """
        try:
            # 确保目录存在
            config_dir = os.path.dirname(self.config_path)
            ensure_dir_exists(config_dir)

            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def _merge_config(self, target: Dict[str, Any], source: Dict[str, Any]):
        """
        递归合并配置字典
        
        参数:
            target (dict): 目标字典
            source (dict): 源字典
        """
        for key, value in source.items():
            if key in target:
                if isinstance(target[key], dict) and isinstance(value, dict):
                    self._merge_config(target[key], value)
                else:
                    target[key] = value
    
    def get(self, key: str, default=None):
        """
        获取配置值
        
        参数:
            key (str): 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        返回:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        参数:
            key (str): 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        target = self.config
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in target:
                target[k] = {}
            target = target[k]
        
        # 设置值
        target[keys[-1]] = value
    
    def get_ffmpeg_path(self) -> str:
        """获取FFmpeg路径"""
        return self.get("ffmpeg_path", "")
    
    def set_ffmpeg_path(self, path: str):
        """设置FFmpeg路径"""
        self.set("ffmpeg_path", path)
        self.save_config()
    
    def get_last_output_dir(self) -> str:
        """获取上次使用的输出目录"""
        return self.get("last_output_dir", "")
    
    def set_last_output_dir(self, path: str):
        """设置上次使用的输出目录"""
        self.set("last_output_dir", path)
        self.save_config()
    
    def get_voice_settings(self) -> Dict[str, Any]:
        """获取语音设置"""
        return self.get("voice_settings", {
            "voice": "zh-CN-XiaoxiaoNeural",
            "rate": 0,
            "pitch": 0,
            "volume": 50  # 支持0-300的音量范围
        })
    
    def set_voice_settings(self, voice: str, rate: int, pitch: int, volume: int):
        """设置语音参数"""
        self.set("voice_settings.voice", voice)
        self.set("voice_settings.rate", rate)
        self.set("voice_settings.pitch", pitch)
        self.set("voice_settings.volume", volume)
        self.save_config()
    
    def get_generate_srt(self) -> bool:
        """获取是否生成字幕设置"""
        return self.get("generate_srt", True)
    
    def set_generate_srt(self, generate: bool):
        """设置是否生成字幕"""
        self.set("generate_srt", generate)
        self.save_config()
    
    def get_window_settings(self) -> Dict[str, int]:
        """获取窗口设置"""
        return self.get("window_settings", {"width": 1000, "height": 700})
    
    def set_window_settings(self, width: int, height: int):
        """设置窗口大小"""
        self.set("window_settings.width", width)
        self.set("window_settings.height", height)
        self.save_config()

    def get_advanced_settings(self) -> Dict[str, Any]:
        """获取高级设置"""
        return self.get("advanced_settings", {
            "segment_length": 3000,  # 增加默认值
            "thread_count": 5,       # 优化多线程性能
            "retry_count": 3,
            "retry_interval": 2,
            # 停顿移除设置已移除
            "enhance_quality": True, # 是否增强音频质量（保持兼容性）
            "enhance_clarity": False, # 是否增强清晰度（默认关闭以提高速度）
            "normalize_volume": False # 是否标准化音量（已移除）
        })

    def set_advanced_settings(self, segment_length: int, thread_count: int, retry_count: int, retry_interval: int,
                            enhance_quality: bool = True, enhance_clarity: bool = False, normalize_volume: bool = False):
        """设置高级参数"""
        self.set("advanced_settings.segment_length", segment_length)
        self.set("advanced_settings.thread_count", thread_count)
        self.set("advanced_settings.retry_count", retry_count)
        self.set("advanced_settings.retry_interval", retry_interval)
        # 停顿移除设置已移除
        self.set("advanced_settings.enhance_quality", enhance_quality)
        self.set("advanced_settings.enhance_clarity", enhance_clarity)
        self.set("advanced_settings.normalize_volume", normalize_volume)
        self.save_config()

    def get_segment_length(self) -> int:
        """获取分块大小"""
        return self.get("advanced_settings.segment_length", 3000)  # 增加默认值

    def get_thread_count(self) -> int:
        """获取线程数"""
        return self.get("advanced_settings.thread_count", 5)

    def get_retry_count(self) -> int:
        """获取重试次数"""
        return self.get("advanced_settings.retry_count", 3)

    def get_retry_interval(self) -> int:
        """获取重试间隔"""
        return self.get("advanced_settings.retry_interval", 2)

    # 停顿移除相关方法已移除

    def get_enhance_quality(self) -> bool:
        """获取是否增强音频质量"""
        return self.get("advanced_settings.enhance_quality", True)

    def get_enhance_clarity(self) -> bool:
        """获取是否增强清晰度"""
        return self.get("advanced_settings.enhance_clarity", True)

    def get_normalize_volume(self) -> bool:
        """获取是否标准化音量"""
        return self.get("advanced_settings.normalize_volume", True)

# 全局配置管理器实例
config_manager = ConfigManager()
