import tkinter as tk
from tkinter import ttk, font

class AppBase:
    def __init__(self, root, title="AI 配音助手"):
        self.root = root
        self.root.title(title)
        self.root.geometry("1300x1000")
        
        # 设置主题颜色
        self.bg_color = "#f8f9fa"  # 浅灰色背景
        self.accent_color = "#4361ee"  # 强调色
        self.text_color = "#212529"  # 文本颜色
        self.sidebar_color = "#343a40"  # 侧边栏颜色
        
        self.root.configure(bg=self.bg_color)
        
        # 加载和设置自定义字体
        self.setup_fonts()
        
        # 创建主框架
        self.main_frame = tk.Frame(self.root, bg=self.bg_color)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建侧边栏
        self.create_sidebar()
        
        # 创建内容区域
        self.create_content_area()
    
    def setup_fonts(self):
        """设置应用程序字体"""
        # 检查常用中文字体
        chinese_fonts = ["微软雅黑", "Microsoft YaHei", "SimHei", "黑体", "SimSun", "宋体", "NSimSun"]
        self.title_font = None
        self.content_font = None
        
        # 获取系统可用字体
        available_fonts = font.families()
        
        # 寻找合适的中文字体
        for f in chinese_fonts:
            if f in available_fonts:
                self.title_font = f
                self.content_font = f
                break
        
        # 如果没有找到中文字体，使用默认字体
        if not self.title_font:
            self.title_font = "TkDefaultFont"
            self.content_font = "TkDefaultFont"
    
    def create_sidebar(self):
        """创建侧边栏基础结构"""
        # 侧边栏框架
        self.sidebar = tk.Frame(self.main_frame, width=220, bg=self.sidebar_color)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar.pack_propagate(False)  # 固定尺寸
        
        # 创建标志/品牌区域
        logo_frame = tk.Frame(self.sidebar, bg=self.sidebar_color, height=120)
        logo_frame.pack(fill=tk.X)
        
        logo_label = tk.Label(
            logo_frame, 
            text="AI 配音助手",
            font=(self.title_font, 20, "bold"),
            fg="#ffffff",
            bg=self.sidebar_color
        )
        logo_label.pack(pady=(30, 10))
        
        # 添加副标题
        subtitle_label = tk.Label(
            logo_frame, 
            text="智能语音生成系统",
            font=(self.content_font, 10),
            fg="#adb5bd",
            bg=self.sidebar_color
        )
        subtitle_label.pack(pady=(0, 20))
        
        # 分隔线
        separator = ttk.Separator(self.sidebar, orient="horizontal")
        separator.pack(fill=tk.X, padx=20, pady=10)
        
        # 底部版本信息
        version_label = tk.Label(
            self.sidebar, 
            text="版本 v1.0.0",
            font=(self.content_font, 8),
            fg="#6c757d",
            bg=self.sidebar_color
        )
        version_label.pack(side=tk.BOTTOM, pady=20)
    
    def create_content_area(self):
        """创建内容区域"""
        # 内容区域使用浅色背景
        self.content = tk.Frame(self.main_frame, bg=self.bg_color)
        self.content.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)