"""
字体管理器
用于搜索系统字体、管理自定义字体和保存字体配置
"""

import os
import json
import platform
import shutil
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont


class FontManager:
    def __init__(self, config_dir="config"):
        """
        初始化字体管理器
        
        参数:
            config_dir (str): 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.font_config_file = self.config_dir / "fonts.json"
        self.custom_fonts_dir = self.config_dir / "custom_fonts"
        self.custom_fonts_dir.mkdir(exist_ok=True)
        
        # 加载字体配置
        self.font_config = self._load_font_config()
    
    def _load_font_config(self):
        """加载字体配置"""
        if self.font_config_file.exists():
            try:
                with open(self.font_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载字体配置失败: {e}")
        
        # 默认配置
        return {
            "system_fonts": {},
            "custom_fonts": {},
            "default_font": None,
            "last_used_font": None
        }
    
    def _save_font_config(self):
        """保存字体配置"""
        try:
            with open(self.font_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.font_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存字体配置失败: {e}")
    
    def scan_system_fonts(self):
        """
        扫描系统字体
        
        返回:
            dict: 字体名称到路径的映射
        """
        system_fonts = {}
        
        if platform.system() == "Windows":
            font_dirs = [
                r"C:\Windows\Fonts",
                r"C:\Windows\System32\Fonts"
            ]
        elif platform.system() == "Darwin":  # macOS
            font_dirs = [
                "/System/Library/Fonts",
                "/Library/Fonts",
                os.path.expanduser("~/Library/Fonts")
            ]
        else:  # Linux
            font_dirs = [
                "/usr/share/fonts",
                "/usr/local/share/fonts",
                os.path.expanduser("~/.fonts"),
                os.path.expanduser("~/.local/share/fonts")
            ]
        
        # 支持的字体文件扩展名
        font_extensions = {'.ttf', '.otf', '.ttc', '.woff', '.woff2'}
        
        for font_dir in font_dirs:
            if os.path.exists(font_dir):
                try:
                    for root, dirs, files in os.walk(font_dir):
                        for file in files:
                            if any(file.lower().endswith(ext) for ext in font_extensions):
                                font_path = os.path.join(root, file)
                                font_name = self._get_font_name(font_path)
                                if font_name:
                                    system_fonts[font_name] = font_path
                except Exception as e:
                    print(f"扫描字体目录 {font_dir} 失败: {e}")
        
        # 更新配置
        self.font_config["system_fonts"] = system_fonts
        self._save_font_config()
        
        return system_fonts
    
    def _get_font_name(self, font_path):
        """
        获取字体文件的显示名称
        
        参数:
            font_path (str): 字体文件路径
        
        返回:
            str: 字体名称
        """
        try:
            # 尝试使用PIL获取字体信息
            font = ImageFont.truetype(font_path, 12)
            # 如果成功加载，使用文件名作为显示名称
            font_name = os.path.splitext(os.path.basename(font_path))[0]
            
            # 特殊处理一些常见字体
            name_mapping = {
                'msyh': '微软雅黑',
                'simhei': 'SimHei',
                'simsun': '宋体',
                'arial': 'Arial',
                'times': 'Times New Roman',
                'calibri': 'Calibri'
            }
            
            font_name_lower = font_name.lower()
            for key, value in name_mapping.items():
                if key in font_name_lower:
                    return value
            
            return font_name
            
        except Exception:
            # 如果无法加载字体，返回None
            return None
    
    def add_custom_font(self, font_path, font_name=None):
        """
        添加自定义字体
        
        参数:
            font_path (str): 字体文件路径
            font_name (str): 自定义字体名称，如果为None则使用文件名
        
        返回:
            bool: 是否成功添加
        """
        try:
            if not os.path.exists(font_path):
                print(f"字体文件不存在: {font_path}")
                return False
            
            # 验证字体文件
            try:
                ImageFont.truetype(font_path, 12)
            except Exception as e:
                print(f"无效的字体文件: {e}")
                return False
            
            # 生成字体名称
            if not font_name:
                font_name = os.path.splitext(os.path.basename(font_path))[0]
            
            # 复制字体文件到自定义字体目录
            font_filename = os.path.basename(font_path)
            custom_font_path = self.custom_fonts_dir / font_filename
            
            # 如果文件已存在，添加数字后缀
            counter = 1
            original_path = custom_font_path
            while custom_font_path.exists():
                name, ext = os.path.splitext(original_path)
                custom_font_path = Path(f"{name}_{counter}{ext}")
                counter += 1
            
            shutil.copy2(font_path, custom_font_path)
            
            # 更新配置
            self.font_config["custom_fonts"][font_name] = str(custom_font_path)
            self._save_font_config()
            
            print(f"成功添加自定义字体: {font_name}")
            return True
            
        except Exception as e:
            print(f"添加自定义字体失败: {e}")
            return False
    
    def remove_custom_font(self, font_name):
        """
        删除自定义字体
        
        参数:
            font_name (str): 字体名称
        
        返回:
            bool: 是否成功删除
        """
        try:
            if font_name in self.font_config["custom_fonts"]:
                font_path = self.font_config["custom_fonts"][font_name]
                
                # 删除字体文件
                if os.path.exists(font_path):
                    os.remove(font_path)
                
                # 从配置中删除
                del self.font_config["custom_fonts"][font_name]
                self._save_font_config()
                
                print(f"成功删除自定义字体: {font_name}")
                return True
            else:
                print(f"自定义字体不存在: {font_name}")
                return False
                
        except Exception as e:
            print(f"删除自定义字体失败: {e}")
            return False
    
    def get_all_fonts(self):
        """
        获取所有可用字体
        
        返回:
            dict: 包含系统字体和自定义字体的字典
        """
        all_fonts = {}
        
        # 添加系统字体
        for name, path in self.font_config["system_fonts"].items():
            all_fonts[f"[系统] {name}"] = path
        
        # 添加自定义字体
        for name, path in self.font_config["custom_fonts"].items():
            all_fonts[f"[自定义] {name}"] = path
        
        return all_fonts
    
    def get_font_path(self, font_name):
        """
        获取字体文件路径
        
        参数:
            font_name (str): 字体名称
        
        返回:
            str: 字体文件路径，如果找不到返回None
        """
        # 移除前缀标记
        clean_name = font_name.replace("[系统] ", "").replace("[自定义] ", "")
        
        # 在系统字体中查找
        if clean_name in self.font_config["system_fonts"]:
            return self.font_config["system_fonts"][clean_name]
        
        # 在自定义字体中查找
        if clean_name in self.font_config["custom_fonts"]:
            return self.font_config["custom_fonts"][clean_name]
        
        return None
    
    def set_default_font(self, font_name):
        """设置默认字体"""
        self.font_config["default_font"] = font_name
        self._save_font_config()
    
    def set_last_used_font(self, font_name):
        """设置最后使用的字体"""
        self.font_config["last_used_font"] = font_name
        self._save_font_config()
    
    def get_default_font(self):
        """获取默认字体"""
        return self.font_config.get("default_font")
    
    def get_last_used_font(self):
        """获取最后使用的字体"""
        return self.font_config.get("last_used_font")
    
    def create_font_preview(self, font_path, text="字体预览 Font Preview", size=(400, 100)):
        """
        创建字体预览图片
        
        参数:
            font_path (str): 字体文件路径
            text (str): 预览文本
            size (tuple): 图片尺寸
        
        返回:
            PIL.Image: 预览图片
        """
        try:
            # 创建图片
            img = Image.new('RGB', size, color='white')
            draw = ImageDraw.Draw(img)
            
            # 加载字体
            font_size = 24
            font = ImageFont.truetype(font_path, font_size)
            
            # 计算文本位置（居中）
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (size[0] - text_width) // 2
            y = (size[1] - text_height) // 2
            
            # 绘制文本
            draw.text((x, y), text, fill='black', font=font)
            
            return img
            
        except Exception as e:
            print(f"创建字体预览失败: {e}")
            # 返回错误提示图片
            img = Image.new('RGB', size, color='lightgray')
            draw = ImageDraw.Draw(img)
            draw.text((10, 40), "无法预览此字体", fill='red')
            return img
