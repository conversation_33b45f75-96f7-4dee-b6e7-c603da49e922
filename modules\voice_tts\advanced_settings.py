"""
高级设置弹窗
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .config import config_manager

class AdvancedSettingsDialog:
    """高级设置对话框"""
    
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        # 创建弹窗
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("高级设置")
        self.dialog.geometry("620x700")
        self.dialog.resizable(True, True)
        
        # 设置模态
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 设置颜色主题
        self.bg_color = "#f8f9fa"
        self.panel_color = "#ffffff"
        self.text_color = "#212529"
        self.accent_color = "#4361ee"
        
        self.dialog.configure(bg=self.bg_color)
        
        # 加载当前设置
        self.load_current_settings()
        
        # 创建界面
        self.create_widgets()

        # 绑定关闭事件 - 点击X按钮时自动保存设置
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_close_window)

        # 存储canvas引用以便清理
        self.canvas = None
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.canvas and self.canvas.winfo_exists():
                # 解绑所有事件
                self.canvas.unbind("<MouseWheel>")
        except tk.TclError:
            pass  # 忽略已销毁的组件

    def center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        
        # 获取窗口大小
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        
        # 获取屏幕大小
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        
        # 计算居中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def load_current_settings(self):
        """加载当前设置"""
        settings = config_manager.get_advanced_settings()
        self.segment_length = settings.get("segment_length", 3000)
        self.thread_count = settings.get("thread_count", 5)
        self.retry_count = settings.get("retry_count", 3)
        self.retry_interval = settings.get("retry_interval", 2)
        # 停顿移除设置已移除，但保留默认值以兼容
        self.remove_pauses = False
        self.max_pause_ms = 100
        # 简化音质增强，只保留清晰度增强，默认关闭以提高速度
        self.enhance_quality = True  # 保持兼容性
        self.enhance_clarity = settings.get("enhance_clarity", False)  # 默认关闭
        self.normalize_volume = False  # 移除音量标准化
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.dialog, bg=self.bg_color, padx=25, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = tk.Label(
            main_frame,
            text="高级设置",
            font=("微软雅黑", 14, "bold"),
            fg=self.text_color,
            bg=self.bg_color
        )
        title_label.pack(pady=(0, 15))

        # 创建滚动区域的容器
        scroll_container = tk.Frame(main_frame, bg=self.bg_color)
        scroll_container.pack(fill=tk.BOTH, expand=True)

        # 创建滚动框架
        canvas = tk.Canvas(scroll_container, bg=self.bg_color, highlightthickness=0)
        scrollbar = ttk.Scrollbar(scroll_container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.bg_color)

        # 存储canvas引用以便清理
        self.canvas = canvas

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 设置面板
        settings_frame = tk.LabelFrame(
            scrollable_frame,
            text="处理参数",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color,
            padx=25,
            pady=20
        )
        settings_frame.pack(fill=tk.X, pady=(0, 25), padx=10)
        
        # 分块大小设置
        self.create_segment_length_setting(settings_frame)
        
        # 线程数设置
        self.create_thread_count_setting(settings_frame)
        
        # 重试次数设置
        self.create_retry_count_setting(settings_frame)
        
        # 重试间隔设置
        self.create_retry_interval_setting(settings_frame)

        # 音频质量增强设置
        self.create_quality_enhancement_setting(settings_frame)

        # 打包滚动框架
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件（只在canvas区域内有效）
        def _on_mousewheel(event):
            try:
                if canvas.winfo_exists():
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except tk.TclError:
                pass  # 忽略canvas已销毁的错误

        # 只在canvas上绑定滚轮事件，而不是全局绑定
        canvas.bind("<MouseWheel>", _on_mousewheel)
        # 为了确保滚轮事件能够触发，需要设置焦点
        canvas.focus_set()

        # 按钮区域（在主框架中，不在滚动区域内）
        button_frame = tk.Frame(main_frame, bg=self.bg_color)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))
        
        # 确定按钮
        ok_btn = tk.Button(
            button_frame,
            text="确定",
            font=("微软雅黑", 10),
            bg=self.accent_color,
            fg="white",
            padx=20,
            pady=8,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.on_ok
        )
        ok_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 取消按钮
        cancel_btn = tk.Button(
            button_frame,
            text="取消",
            font=("微软雅黑", 10),
            bg="#6c757d",
            fg="white",
            padx=20,
            pady=8,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.on_cancel
        )
        cancel_btn.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 重置按钮
        reset_btn = tk.Button(
            button_frame,
            text="重置默认",
            font=("微软雅黑", 10),
            bg="#ffc107",
            fg="black",
            padx=15,
            pady=8,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.on_reset
        )
        reset_btn.pack(side=tk.LEFT)
    
    def create_segment_length_setting(self, parent):
        """创建分块大小设置"""
        frame = tk.Frame(parent, bg=self.panel_color)
        frame.pack(fill=tk.X, pady=12)
        
        # 标签和输入框
        label_frame = tk.Frame(frame, bg=self.panel_color)
        label_frame.pack(fill=tk.X)
        
        tk.Label(
            label_frame,
            text="分块大小 (1000-5000 字符):",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color
        ).pack(side=tk.LEFT)
        
        self.segment_length_var = tk.StringVar(value=str(self.segment_length))
        entry = tk.Entry(
            label_frame,
            textvariable=self.segment_length_var,
            font=("微软雅黑", 9),
            width=8,
            justify=tk.CENTER
        )
        entry.pack(side=tk.RIGHT)
        entry.bind('<KeyRelease>', self.on_segment_length_change)
        
        # 滑块
        self.segment_length_scale_var = tk.IntVar(value=self.segment_length)
        scale = tk.Scale(
            frame,
            from_=1000,
            to=5000,
            orient=tk.HORIZONTAL,
            variable=self.segment_length_scale_var,
            font=("微软雅黑", 8),
            bg=self.panel_color,
            fg=self.text_color,
            highlightthickness=0,
            command=self.update_segment_length_from_scale
        )
        scale.pack(fill=tk.X, pady=(5, 0))

        # 说明
        desc = tk.Label(
            frame,
            text="每个分块的最大字符数，较大的值可能导致单次处理时间较长",
            font=("微软雅黑", 8),
            fg="#6c757d",
            bg=self.panel_color
        )
        desc.pack(anchor=tk.W, pady=(8, 0))
    
    def create_thread_count_setting(self, parent):
        """创建线程数设置"""
        frame = tk.Frame(parent, bg=self.panel_color)
        frame.pack(fill=tk.X, pady=12)
        
        # 标签和输入框
        label_frame = tk.Frame(frame, bg=self.panel_color)
        label_frame.pack(fill=tk.X)
        
        tk.Label(
            label_frame,
            text="线程数 (1-20):",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color
        ).pack(side=tk.LEFT)
        
        self.thread_count_var = tk.StringVar(value=str(self.thread_count))
        entry = tk.Entry(
            label_frame,
            textvariable=self.thread_count_var,
            font=("微软雅黑", 9),
            width=8,
            justify=tk.CENTER
        )
        entry.pack(side=tk.RIGHT)
        entry.bind('<KeyRelease>', self.on_thread_count_change)
        
        # 滑块
        self.thread_count_scale_var = tk.IntVar(value=self.thread_count)
        scale = tk.Scale(
            frame,
            from_=1,
            to=20,
            orient=tk.HORIZONTAL,
            variable=self.thread_count_scale_var,
            font=("微软雅黑", 8),
            bg=self.panel_color,
            fg=self.text_color,
            highlightthickness=0,
            command=self.update_thread_count_from_scale
        )
        scale.pack(fill=tk.X, pady=(5, 0))

        # 说明
        desc = tk.Label(
            frame,
            text="并发处理分块的数量，使用异步优化提升性能（推荐5-10）",
            font=("微软雅黑", 8),
            fg="#6c757d",
            bg=self.panel_color
        )
        desc.pack(anchor=tk.W, pady=(8, 0))
    
    def create_retry_count_setting(self, parent):
        """创建重试次数设置"""
        frame = tk.Frame(parent, bg=self.panel_color)
        frame.pack(fill=tk.X, pady=12)
        
        # 标签和输入框
        label_frame = tk.Frame(frame, bg=self.panel_color)
        label_frame.pack(fill=tk.X)
        
        tk.Label(
            label_frame,
            text="重试次数 (0-10):",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color
        ).pack(side=tk.LEFT)
        
        self.retry_count_var = tk.StringVar(value=str(self.retry_count))
        entry = tk.Entry(
            label_frame,
            textvariable=self.retry_count_var,
            font=("微软雅黑", 9),
            width=8,
            justify=tk.CENTER
        )
        entry.pack(side=tk.RIGHT)
        entry.bind('<KeyRelease>', self.on_retry_count_change)
        
        # 滑块
        self.retry_count_scale_var = tk.IntVar(value=self.retry_count)
        scale = tk.Scale(
            frame,
            from_=0,
            to=10,
            orient=tk.HORIZONTAL,
            variable=self.retry_count_scale_var,
            font=("微软雅黑", 8),
            bg=self.panel_color,
            fg=self.text_color,
            highlightthickness=0,
            command=self.update_retry_count_from_scale
        )
        scale.pack(fill=tk.X, pady=(5, 0))

        # 说明
        desc = tk.Label(
            frame,
            text="分块处理失败时的重试次数",
            font=("微软雅黑", 8),
            fg="#6c757d",
            bg=self.panel_color
        )
        desc.pack(anchor=tk.W, pady=(8, 0))

    def create_retry_interval_setting(self, parent):
        """创建重试间隔设置"""
        frame = tk.Frame(parent, bg=self.panel_color)
        frame.pack(fill=tk.X, pady=12)

        # 标签和输入框
        label_frame = tk.Frame(frame, bg=self.panel_color)
        label_frame.pack(fill=tk.X)

        tk.Label(
            label_frame,
            text="重试间隔 (1-30 秒):",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color
        ).pack(side=tk.LEFT)

        self.retry_interval_var = tk.StringVar(value=str(self.retry_interval))
        entry = tk.Entry(
            label_frame,
            textvariable=self.retry_interval_var,
            font=("微软雅黑", 9),
            width=8,
            justify=tk.CENTER
        )
        entry.pack(side=tk.RIGHT)
        entry.bind('<KeyRelease>', self.on_retry_interval_change)

        # 滑块
        self.retry_interval_scale_var = tk.IntVar(value=self.retry_interval)
        scale = tk.Scale(
            frame,
            from_=1,
            to=30,
            orient=tk.HORIZONTAL,
            variable=self.retry_interval_scale_var,
            font=("微软雅黑", 8),
            bg=self.panel_color,
            fg=self.text_color,
            highlightthickness=0,
            command=self.update_retry_interval_from_scale
        )
        scale.pack(fill=tk.X, pady=(5, 0))

        # 说明
        desc = tk.Label(
            frame,
            text="重试之间的等待时间",
            font=("微软雅黑", 8),
            fg="#6c757d",
            bg=self.panel_color
        )
        desc.pack(anchor=tk.W, pady=(8, 0))

    # 停顿移除设置已移除
    # def create_pause_removal_setting(self, parent):
    #     pass

    def create_quality_enhancement_setting(self, parent):
        """创建音频质量增强设置"""
        frame = tk.Frame(parent, bg=self.panel_color)
        frame.pack(fill=tk.X, pady=12)

        # 增强咬字清晰度复选框
        self.enhance_clarity_var = tk.BooleanVar(value=self.enhance_clarity)
        clarity_checkbox = tk.Checkbutton(
            frame,
            text="增强咬字清晰度（FFmpeg快速处理）",
            variable=self.enhance_clarity_var,
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color,
            activebackground=self.panel_color
        )
        clarity_checkbox.pack(anchor=tk.W, pady=2)

        # 说明文字
        desc = tk.Label(
            frame,
            text="使用FFmpeg高通滤波增强咬字清晰度，比传统方法快很多。可根据需要启用。",
            font=("微软雅黑", 9),
            fg="#6c757d",
            bg=self.panel_color
        )
        desc.pack(anchor=tk.W, pady=(8, 0))

    def update_segment_length_from_scale(self, value):
        """从滑块更新分块大小"""
        self.segment_length_var.set(str(value))

    def update_thread_count_from_scale(self, value):
        """从滑块更新线程数"""
        self.thread_count_var.set(str(value))

    def update_retry_count_from_scale(self, value):
        """从滑块更新重试次数"""
        self.retry_count_var.set(str(value))

    def update_retry_interval_from_scale(self, value):
        """从滑块更新重试间隔"""
        self.retry_interval_var.set(str(value))

    def on_segment_length_change(self, event=None):
        """处理分块大小输入框变化"""
        try:
            text = self.segment_length_var.get()
            if text:
                value = int(text)
                if 1000 <= value <= 5000:
                    self.segment_length_scale_var.set(value)
        except ValueError:
            pass

    def on_thread_count_change(self, event=None):
        """处理线程数输入框变化"""
        try:
            text = self.thread_count_var.get()
            if text:
                value = int(text)
                if 1 <= value <= 20:
                    self.thread_count_scale_var.set(value)
        except ValueError:
            pass

    def on_retry_count_change(self, event=None):
        """处理重试次数输入框变化"""
        try:
            text = self.retry_count_var.get()
            if text:
                value = int(text)
                if 0 <= value <= 10:
                    self.retry_count_scale_var.set(value)
        except ValueError:
            pass

    def on_retry_interval_change(self, event=None):
        """处理重试间隔输入框变化"""
        try:
            text = self.retry_interval_var.get()
            if text:
                value = int(text)
                if 1 <= value <= 30:
                    self.retry_interval_scale_var.set(value)
        except ValueError:
            pass

    # 停顿移除相关事件处理已移除

    def on_enhance_quality_change(self):
        """处理音频质量增强复选框变化"""
        # 这里可以添加启用/禁用相关控件的逻辑
        pass

    def validate_settings(self):
        """验证设置值"""
        try:
            segment_length = int(self.segment_length_var.get())
            thread_count = int(self.thread_count_var.get())
            retry_count = int(self.retry_count_var.get())
            retry_interval = int(self.retry_interval_var.get())
            # 简化音质增强设置
            enhance_quality = True  # 保持兼容性
            enhance_clarity = self.enhance_clarity_var.get()
            normalize_volume = False  # 移除音量标准化

            if not (1000 <= segment_length <= 5000):
                raise ValueError("分块大小必须在 1000-5000 之间")
            if not (1 <= thread_count <= 20):
                raise ValueError("线程数必须在 1-20 之间")
            if not (0 <= retry_count <= 10):
                raise ValueError("重试次数必须在 0-10 之间")
            if not (1 <= retry_interval <= 30):
                raise ValueError("重试间隔必须在 1-30 秒之间")
            # 停顿时长验证已移除

            return {
                "segment_length": segment_length,
                "thread_count": thread_count,
                "retry_count": retry_count,
                "retry_interval": retry_interval,
                # 停顿移除设置已移除
                "enhance_quality": enhance_quality,
                "enhance_clarity": enhance_clarity,
                "normalize_volume": normalize_volume
            }
        except ValueError as e:
            messagebox.showerror("输入错误", str(e))
            return None

    def on_ok(self):
        """确定按钮点击事件"""
        settings = self.validate_settings()
        if settings:
            # 保存设置
            config_manager.set_advanced_settings(
                settings["segment_length"],
                settings["thread_count"],
                settings["retry_count"],
                settings["retry_interval"],
                settings["enhance_quality"],
                settings["enhance_clarity"],
                settings["normalize_volume"]
            )
            self.result = settings
            self.cleanup()
            self.dialog.destroy()

    def on_cancel(self):
        """取消按钮点击事件"""
        self.result = None
        self.cleanup()
        self.dialog.destroy()

    def on_close_window(self):
        """窗口关闭事件 - 自动保存设置"""
        # 验证并保存设置
        settings = self.validate_settings()
        if settings:
            # 保存设置
            config_manager.set_advanced_settings(
                settings["segment_length"],
                settings["thread_count"],
                settings["retry_count"],
                settings["retry_interval"],
                settings["enhance_quality"],
                settings["enhance_clarity"],
                settings["normalize_volume"]
            )
            self.result = settings
        else:
            # 如果验证失败，使用当前值
            self.result = {
                "segment_length": self.segment_length,
                "thread_count": self.thread_count,
                "retry_count": self.retry_count,
                "retry_interval": self.retry_interval,
                # 停顿移除设置已移除
                "enhance_quality": self.enhance_quality,
                "enhance_clarity": self.enhance_clarity,
                "normalize_volume": self.normalize_volume
            }
        self.cleanup()
        self.dialog.destroy()

    def on_reset(self):
        """重置为默认值"""
        self.segment_length_var.set("3000")
        self.thread_count_var.set("5")
        self.retry_count_var.set("3")
        self.retry_interval_var.set("2")
        # 简化音质增强设置，默认关闭以提高速度
        self.enhance_clarity_var.set(False)

        self.segment_length_scale_var.set(3000)
        self.thread_count_scale_var.set(5)
        self.retry_count_scale_var.set(3)
        self.retry_interval_scale_var.set(2)
