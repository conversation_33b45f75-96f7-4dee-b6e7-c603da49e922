"""
演示如何解决FFmpeg中文路径问题的方案
"""

import os
import tempfile
import shutil
import subprocess
import uuid

class PathFixDemo:
    """路径修复演示类"""
    
    def __init__(self):
        self.temp_dir = None
    
    def create_temp_workspace(self):
        """创建临时工作空间，避免中文路径问题"""
        try:
            # 创建临时目录（通常在系统临时目录下，路径为英文）
            self.temp_dir = tempfile.mkdtemp(prefix="video_composer_")
            print(f"创建临时工作空间: {self.temp_dir}")
            return self.temp_dir
        except Exception as e:
            print(f"创建临时工作空间失败: {e}")
            return None
    
    def copy_files_to_temp(self, original_files):
        """将原始文件复制到临时目录，使用英文文件名"""
        if not self.temp_dir:
            self.create_temp_workspace()
        
        temp_files = {}
        
        for file_type, original_path in original_files.items():
            if not original_path or not os.path.exists(original_path):
                continue
            
            # 生成英文临时文件名
            file_ext = os.path.splitext(original_path)[1]
            temp_filename = f"{file_type}_{uuid.uuid4().hex[:8]}{file_ext}"
            temp_path = os.path.join(self.temp_dir, temp_filename)
            
            try:
                shutil.copy2(original_path, temp_path)
                temp_files[file_type] = temp_path
                print(f"复制文件: {os.path.basename(original_path)} -> {temp_filename}")
            except Exception as e:
                print(f"复制文件失败 {original_path}: {e}")
        
        return temp_files
    
    def build_ffmpeg_command_safe(self, temp_files, output_path, settings):
        """构建安全的FFmpeg命令（使用临时文件路径）"""
        
        # 确保输出路径也是英文的
        output_dir = os.path.dirname(output_path)
        output_filename = os.path.basename(output_path)
        temp_output = os.path.join(self.temp_dir, f"output_{uuid.uuid4().hex[:8]}.mp4")
        
        cmd = ['ffmpeg', '-y']
        
        # 输入文件
        if 'video' in temp_files:
            cmd.extend(['-i', temp_files['video']])
        
        if 'audio' in temp_files:
            cmd.extend(['-i', temp_files['audio']])
        
        # 视频处理
        filter_complex = '[0:v]loop=-1:size=32767:start=0[v];[v]scale=iw:ih'
        
        # 字幕处理（关键改进）
        if 'subtitle' in temp_files:
            # 使用临时文件路径，避免中文路径问题
            subtitle_path = temp_files['subtitle'].replace('\\', '/')
            filter_complex += f",subtitles='{subtitle_path}':force_style='FontName=Arial,FontSize=22,PrimaryColour=&HFFFFFF&'"
        
        filter_complex += '[final]'
        
        cmd.extend(['-filter_complex', filter_complex])
        cmd.extend(['-map', '[final]', '-map', '1:a'])
        cmd.extend(['-shortest'])
        cmd.extend(['-c:v', 'libx264', '-c:a', 'aac'])
        cmd.extend(['-preset', 'medium', '-crf', '23'])
        cmd.append(temp_output)
        
        return cmd, temp_output
    
    def execute_ffmpeg_safe(self, original_files, final_output_path, settings):
        """安全执行FFmpeg命令"""
        try:
            print("开始安全的FFmpeg处理...")
            
            # 1. 创建临时工作空间
            if not self.create_temp_workspace():
                return False
            
            # 2. 复制文件到临时目录
            temp_files = self.copy_files_to_temp(original_files)
            if not temp_files:
                print("没有有效的输入文件")
                return False
            
            # 3. 构建安全的FFmpeg命令
            cmd, temp_output = self.build_ffmpeg_command_safe(temp_files, final_output_path, settings)
            
            print("FFmpeg命令:")
            print(" ".join(cmd))
            
            # 4. 执行FFmpeg命令（隐藏窗口）
            print("执行FFmpeg命令...")
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("FFmpeg执行成功")
                
                # 5. 将结果复制到最终位置
                if os.path.exists(temp_output):
                    shutil.copy2(temp_output, final_output_path)
                    print(f"输出文件已保存到: {final_output_path}")
                    return True
                else:
                    print("临时输出文件不存在")
                    return False
            else:
                print(f"FFmpeg执行失败: {result.stderr}")
                return False
        
        except Exception as e:
            print(f"安全执行失败: {e}")
            return False
        
        finally:
            # 清理临时文件
            self.cleanup_temp()
    
    def cleanup_temp(self):
        """清理临时文件"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                print(f"清理临时目录: {self.temp_dir}")
            except Exception as e:
                print(f"清理临时目录失败: {e}")
            finally:
                self.temp_dir = None

def demo_path_fix():
    """演示路径修复方案"""
    print("🔧 FFmpeg中文路径问题解决方案演示")
    print("="*50)
    
    # 模拟有中文路径的文件
    original_files = {
        'video': r'd:\jiedan\7.5 AI配音\data\微信视频2025-07-20_022016_851.mp4',
        'audio': r'd:\jiedan\7.5 AI配音\data\d2d87b你结_1.mp3',
        'subtitle': r'd:\jiedan\7.5 AI配音\data\d2d87b你结_1.srt'
    }
    
    # 检查文件是否存在
    existing_files = {}
    for file_type, path in original_files.items():
        if os.path.exists(path):
            existing_files[file_type] = path
            print(f"✓ 找到{file_type}文件: {os.path.basename(path)}")
        else:
            print(f"✗ 未找到{file_type}文件: {path}")
    
    if not existing_files:
        print("没有找到测试文件，创建演示...")
        
        # 创建演示
        demo = PathFixDemo()
        temp_dir = demo.create_temp_workspace()
        
        if temp_dir:
            print(f"\n临时工作空间: {temp_dir}")
            print("这个路径是纯英文的，FFmpeg可以正常处理")
            
            # 演示文件名转换
            chinese_files = [
                "d2d87b你结_1_1.srt",
                "微信视频2025-07-20_022016_851.mp4",
                "d2d87b你结_1.mp3"
            ]
            
            print("\n文件名转换演示:")
            for chinese_name in chinese_files:
                file_ext = os.path.splitext(chinese_name)[1]
                english_name = f"temp_{uuid.uuid4().hex[:8]}{file_ext}"
                print(f"  {chinese_name} -> {english_name}")
            
            demo.cleanup_temp()
        
        return
    
    # 执行实际的路径修复演示
    output_path = r'd:\jiedan\7.5 AI配音\output\demo_fixed.mp4'
    settings = {
        'resolution': '960x720',
        'fps': 30
    }
    
    demo = PathFixDemo()
    success = demo.execute_ffmpeg_safe(existing_files, output_path, settings)
    
    if success:
        print("\n🎉 路径修复方案演示成功！")
        print("主要改进:")
        print("1. 使用临时英文目录避免中文路径")
        print("2. 复制文件到临时位置，使用英文文件名")
        print("3. FFmpeg处理临时文件，避免编码问题")
        print("4. 处理完成后复制到最终位置")
        print("5. 自动清理临时文件")
    else:
        print("\n❌ 演示失败，但方案思路正确")

if __name__ == "__main__":
    demo_path_fix()
