import tkinter as tk
from tkinter import ttk
import tkinter.font as tkfont

# 自定义样式按钮（使用Frame实现，而不是Canvas+PIL）
class StyleButton:
    def __init__(self, master, text, width=120, height=40, 
                 bg_color="#3a0ca3", fg_color="#ffffff",
                 hover_color="#7209b7", active_color="#f72585",
                 font_size=12, command=None):
        self.master = master
        self.text = text
        self.width = width
        self.height = height
        self.bg_color = bg_color
        self.fg_color = fg_color
        self.hover_color = hover_color
        self.active_color = active_color
        self.font_size = font_size
        self.command = command
        
        # 创建按钮框架
        self.frame = tk.Frame(master, width=width, height=height)
        self.frame.pack_propagate(False)  # 固定尺寸
        
        # 创建按钮
        self.button = tk.Label(
            self.frame,
            text=text,
            bg=bg_color,
            fg=fg_color,
            font=("微软雅黑", font_size),
            cursor="hand2"
        )
        self.button.pack(fill=tk.BOTH, expand=True)
        
        # 绑定事件
        self.button.bind("<Enter>", self.on_enter)
        self.button.bind("<Leave>", self.on_leave)
        self.button.bind("<Button-1>", self.on_press)
        self.button.bind("<ButtonRelease-1>", self.on_release)
    
    def on_enter(self, event):
        self.button.config(bg=self.hover_color)
    
    def on_leave(self, event):
        self.button.config(bg=self.bg_color)
    
    def on_press(self, event):
        self.button.config(bg=self.active_color)
    
    def on_release(self, event):
        self.button.config(bg=self.hover_color)
        if self.command:
            self.command()
    
    def pack(self, **kwargs):
        self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        self.frame.grid(**kwargs)

# 侧边栏按钮类
class SidebarButton(StyleButton):
    def __init__(self, master, text, command=None):
        super().__init__(
            master=master, 
            text=text, 
            width=180, 
            height=40, 
            bg_color="#4361ee", 
            hover_color="#3a0ca3", 
            active_color="#7209b7",
            fg_color="#ffffff",
            font_size=14,
            command=command
        )

# 操作按钮类
class ActionButton(StyleButton):
    def __init__(self, master, text, command=None):
        super().__init__(
            master=master, 
            text=text, 
            width=150, 
            height=50, 
            bg_color="#f72585", 
            hover_color="#7209b7", 
            active_color="#3a0ca3",
            fg_color="#ffffff",
            font_size=16,
            command=command
        )

# 功能按钮类
class FeatureButton(StyleButton):
    def __init__(self, master, text, command=None):
        super().__init__(
            master=master, 
            text=text, 
            width=120, 
            height=80, 
            bg_color="#4cc9f0", 
            hover_color="#4361ee", 
            active_color="#3a0ca3",
            fg_color="#ffffff",
            font_size=14,
            command=command
        ) 