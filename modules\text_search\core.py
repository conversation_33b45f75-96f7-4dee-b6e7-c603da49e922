import re

class TextSearchEngine:
    """文本搜索引擎核心类"""
    
    def __init__(self):
        self.text = ""
        self.lines = []
    
    def set_text(self, text):
        """设置要搜索的文本内容"""
        self.text = text
        self.lines = text.split('\n')
    
    def search(self, search_text, case_sensitive=False, max_results=None):
        """
        搜索文本（带防卡死保护）

        参数:
            search_text (str): 要搜索的文本
            case_sensitive (bool): 是否区分大小写
            max_results (int): 最大结果数量，防止卡死

        返回:
            list: 包含匹配结果的列表，每项为 (行号, 行文本, 匹配位置)
        """
        results = []

        # 如果没有文本，直接返回空结果
        if not self.text or not search_text:
            return results

        # 如果搜索文本为空，返回空结果
        if len(search_text.strip()) < 1:
            return results

        # 根据搜索文本长度设置默认最大结果数
        if max_results is None:
            if len(search_text) == 1:
                max_results = 20000  # 单字符搜索限制20000个结果
            else:
                max_results = 50000  # 多字符搜索限制50000个结果

        # 设置搜索标志
        flags = 0 if case_sensitive else re.IGNORECASE

        try:
            # 遍历每一行查找匹配
            for line_num, line in enumerate(self.lines, 1):
                # 如果结果数量达到上限，停止搜索
                if len(results) >= max_results:
                    break

                # 查找所有匹配项
                for match in re.finditer(re.escape(search_text), line, flags):
                    start_pos = match.start()
                    results.append((line_num, line, start_pos))

                    # 如果结果数量达到上限，停止搜索
                    if len(results) >= max_results:
                        break
        except Exception as e:
            print(f"搜索过程中出错: {e}")
            # 即使出错也返回已找到的结果

        return results
    
    def replace(self, search_text, replace_text, line_num=None, case_sensitive=False):
        """
        替换文本
        
        参数:
            search_text (str): 要搜索的文本
            replace_text (str): 要替换的文本
            line_num (int, optional): 特定行号。如果提供，只替换该行
            case_sensitive (bool): 是否区分大小写
        
        返回:
            str: 替换后的文本
        """
        if not self.text or not search_text:
            return self.text
        
        # 设置替换标志
        flags = 0 if case_sensitive else re.IGNORECASE
        
        if line_num is None:
            # 替换全文
            return re.sub(re.escape(search_text), replace_text, self.text, flags=flags)
        else:
            # 只替换指定行
            if 1 <= line_num <= len(self.lines):
                self.lines[line_num-1] = re.sub(
                    re.escape(search_text), 
                    replace_text, 
                    self.lines[line_num-1], 
                    flags=flags
                )
                return '\n'.join(self.lines)
            return self.text 