import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
import time
import datetime
from .core import TextProcessor

class TextProcessorWindow:
    def __init__(self, parent):
        self.parent = parent
        self.processor = TextProcessor()
        
        # 设置颜色主题
        self.bg_color = "#f8f9fa"
        self.panel_color = "#ffffff"
        self.text_color = "#212529"
        self.accent_color = "#4361ee"
        self.button_text_color = "#ffffff"
        
        # 创建主框架
        self.frame = tk.Frame(parent, bg=self.bg_color, padx=15, pady=10)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件列表和选择状态
        self.file_paths = []
        self.file_vars = []
        
        # 创建界面
        self.create_top_section()
        self.create_main_section()
        self.create_bottom_section()
    
    def create_top_section(self):
        """创建顶部区域，包含标题和文件导入按钮"""
        top_frame = tk.Frame(self.frame, bg=self.bg_color)
        top_frame.pack(fill=tk.X, pady=10)
        
        # 标题
        title_label = tk.Label(
            top_frame,
            text="文本处理",
            font=("微软雅黑", 16, "bold"),
            fg=self.text_color,
            bg=self.bg_color
        )
        title_label.pack(side=tk.LEFT, padx=10)
        
        # 导入文件按钮
        self.import_files_btn = tk.Button(
            top_frame,
            text="导入文件",
            font=("微软雅黑", 10),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=15,
            pady=5,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.import_files
        )
        self.import_files_btn.pack(side=tk.RIGHT, padx=5)
        
        # 导入文件夹按钮
        self.import_folder_btn = tk.Button(
            top_frame,
            text="导入文件夹",
            font=("微软雅黑", 10),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=15,
            pady=5,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.import_folder
        )
        self.import_folder_btn.pack(side=tk.RIGHT, padx=5)
    
    def create_main_section(self):
        """创建主区域，包含文件列表和操作区域"""
        main_frame = tk.Frame(self.frame, bg=self.bg_color)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧文件列表区域
        left_frame = tk.Frame(main_frame, bg=self.panel_color, padx=10, pady=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 文件列表标签
        files_label = tk.Label(
            left_frame,
            text="文件列表:",
            font=("微软雅黑", 12, "bold"),
            fg=self.text_color,
            bg=self.panel_color
        )
        files_label.pack(anchor=tk.W, pady=5)
        
        # 创建文件列表框架
        files_frame = tk.Frame(left_frame, bg=self.panel_color)
        files_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表头
        header_frame = tk.Frame(files_frame, bg="#f0f0f0", height=30)
        header_frame.pack(fill=tk.X, pady=(0, 0))
        header_frame.pack_propagate(False)  # 防止框架收缩

        # 表头列
        check_header = tk.Label(header_frame, text="选择", width=5, anchor=tk.CENTER, bg="#f0f0f0", relief=tk.RAISED, font=("微软雅黑", 9, "bold"))
        check_header.grid(row=0, column=0, sticky="ew", padx=1, pady=1)

        name_header = tk.Label(header_frame, text="文件名", width=25, anchor=tk.W, bg="#f0f0f0", relief=tk.RAISED, font=("微软雅黑", 9, "bold"))
        name_header.grid(row=0, column=1, sticky="ew", padx=1, pady=1)

        size_header = tk.Label(header_frame, text="字数", width=10, anchor=tk.CENTER, bg="#f0f0f0", relief=tk.RAISED, font=("微软雅黑", 9, "bold"))
        size_header.grid(row=0, column=2, sticky="ew", padx=1, pady=1)

        duration_header = tk.Label(header_frame, text="预计时长", width=12, anchor=tk.CENTER, bg="#f0f0f0", relief=tk.RAISED, font=("微软雅黑", 9, "bold"))
        duration_header.grid(row=0, column=3, sticky="ew", padx=1, pady=1)

        modified_header = tk.Label(header_frame, text="修改时间", width=15, anchor=tk.CENTER, bg="#f0f0f0", relief=tk.RAISED, font=("微软雅黑", 9, "bold"))
        modified_header.grid(row=0, column=4, sticky="ew", padx=1, pady=1)

        # 设置列权重
        header_frame.columnconfigure(1, weight=1)

        # 创建文件列表容器（紧贴表头）
        list_container = tk.Frame(files_frame, bg="white")
        list_container.pack(fill=tk.BOTH, expand=True, pady=(0, 0))

        # 创建滚动条
        scrollbar_y = ttk.Scrollbar(list_container)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建Canvas用于滚动
        self.files_canvas = tk.Canvas(
            list_container,
            bg=self.panel_color,
            bd=1,
            relief=tk.SUNKEN,
            highlightthickness=0,
            yscrollcommand=scrollbar_y.set
        )
        self.files_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 配置滚动条
        scrollbar_y.config(command=self.files_canvas.yview)

        # 创建内部框架用于放置文件项
        self.files_inner_frame = tk.Frame(self.files_canvas, bg=self.panel_color)
        self.files_canvas.create_window((0, 0), window=self.files_inner_frame, anchor=tk.NW)

        # 绑定事件，确保内部框架大小更新时，Canvas的滚动区域也更新
        self.files_inner_frame.bind("<Configure>", self._on_frame_configure)
        self.files_canvas.bind("<Configure>", self._on_canvas_configure)

        # 绑定鼠标滚轮事件到多个组件
        self._bind_mousewheel_to_widget(self.files_canvas)
        self._bind_mousewheel_to_widget(self.files_inner_frame)
        self._bind_mousewheel_to_widget(list_container)
        self._bind_mousewheel_to_widget(files_frame)
        
        # 文件操作按钮
        file_buttons_frame = tk.Frame(left_frame, bg=self.panel_color)
        file_buttons_frame.pack(fill=tk.X, pady=5)
        
        # 全选按钮
        self.select_all_btn = tk.Button(
            file_buttons_frame,
            text="全选",
            font=("微软雅黑", 9),
            bg=self.accent_color,
            fg=self.button_text_color,
            relief=tk.FLAT,
            padx=10,
            command=self.select_all_files
        )
        self.select_all_btn.pack(side=tk.LEFT, padx=5)
        
        # 取消全选按钮
        self.deselect_all_btn = tk.Button(
            file_buttons_frame,
            text="取消全选",
            font=("微软雅黑", 9),
            bg=self.accent_color,
            fg=self.button_text_color,
            relief=tk.FLAT,
            padx=10,
            command=self.deselect_all_files
        )
        self.deselect_all_btn.pack(side=tk.LEFT, padx=5)
        
        # 删除选中文件按钮
        self.delete_selected_btn = tk.Button(
            file_buttons_frame,
            text="删除选中",
            font=("微软雅黑", 9),
            bg="#dc3545",  # 红色按钮
            fg=self.button_text_color,
            relief=tk.FLAT,
            padx=10,
            command=self.delete_selected_files
        )
        self.delete_selected_btn.pack(side=tk.LEFT, padx=5)
        
        # 右侧操作区域
        right_frame = tk.Frame(main_frame, bg=self.panel_color, padx=10, pady=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, padx=5, pady=5, expand=True)
        
        # 操作区标题
        operations_label = tk.Label(
            right_frame,
            text="文本处理操作:",
            font=("微软雅黑", 12, "bold"),
            fg=self.text_color,
            bg=self.panel_color
        )
        operations_label.pack(anchor=tk.W, pady=5)
        
        # 文本格式化区域
        format_frame = tk.LabelFrame(
            right_frame,
            text="文本格式化",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color,
            padx=10,
            pady=10
        )
        format_frame.pack(fill=tk.X, pady=10)
        
        # 格式化选项
        format_options_frame = tk.Frame(format_frame, bg=self.panel_color)
        format_options_frame.pack(fill=tk.X, pady=5)
        
        # 是否启用格式化
        self.format_var = tk.BooleanVar(value=True)
        self.format_check = tk.Checkbutton(
            format_options_frame,
            text="启用文本格式化",
            variable=self.format_var,
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        self.format_check.pack(anchor=tk.W)
        
        format_desc = tk.Label(
            format_options_frame,
            text="将在标点符号处另起一行并删除标点符号，同时删除标题行",
            font=("微软雅黑", 9),
            fg="#6c757d",
            bg=self.panel_color
        )
        format_desc.pack(anchor=tk.W, pady=(0, 5))
        
        # 文本分割区域
        split_frame = tk.LabelFrame(
            right_frame,
            text="文本分割",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color,
            padx=10,
            pady=10
        )
        split_frame.pack(fill=tk.X, pady=10)
        
        # 分割选项
        split_options_frame = tk.Frame(split_frame, bg=self.panel_color)
        split_options_frame.pack(fill=tk.X, pady=5)
        
        # 是否启用分割
        self.split_var = tk.BooleanVar(value=False)
        self.split_check = tk.Checkbutton(
            split_options_frame,
            text="启用文本分割",
            variable=self.split_var,
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        self.split_check.pack(anchor=tk.W)
        
        # 首个文件字数限制
        first_limit_frame = tk.Frame(split_options_frame, bg=self.panel_color)
        first_limit_frame.pack(fill=tk.X, pady=5)
        
        first_limit_label = tk.Label(
            first_limit_frame,
            text="首个文件字数:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        first_limit_label.grid(row=0, column=0, sticky=tk.W, padx=5)
        
        self.first_limit_var = tk.StringVar(value="150000")
        self.first_limit_entry = tk.Entry(
            first_limit_frame,
            textvariable=self.first_limit_var,
            font=("微软雅黑", 10),
            width=10
        )
        self.first_limit_entry.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        # 其他文件字数限制
        other_limit_frame = tk.Frame(split_options_frame, bg=self.panel_color)
        other_limit_frame.pack(fill=tk.X, pady=5)
        
        other_limit_label = tk.Label(
            other_limit_frame,
            text="后续文件字数:",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color
        )
        other_limit_label.grid(row=0, column=0, sticky=tk.W, padx=5)
        
        self.other_limit_var = tk.StringVar(value="200000")
        self.other_limit_entry = tk.Entry(
            other_limit_frame,
            textvariable=self.other_limit_var,
            font=("微软雅黑", 10),
            width=10
        )
        self.other_limit_entry.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        # 处理按钮
        process_btn = tk.Button(
            right_frame,
            text="开始处理",
            font=("微软雅黑", 12),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=20,
            pady=10,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.process_selected_files
        )
        process_btn.pack(pady=20)
    
    def create_bottom_section(self):
        """创建底部状态栏"""
        bottom_frame = tk.Frame(self.frame, bg="#e9ecef", height=30)
        bottom_frame.pack(fill=tk.X, pady=5)
        
        # 状态标签
        self.status_label = tk.Label(
            bottom_frame,
            text="就绪",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg="#e9ecef",
            anchor="w",
            padx=10,
            pady=5
        )
        self.status_label.pack(fill=tk.X)
    
    def _on_frame_configure(self, event):
        """当内部框架大小改变时，更新Canvas的滚动区域"""
        self.files_canvas.configure(scrollregion=self.files_canvas.bbox("all"))
    
    def _on_canvas_configure(self, event):
        """当Canvas大小改变时，调整内部框架的宽度"""
        # 调整内部框架宽度以适应Canvas
        self.files_canvas.itemconfig(
            self.files_canvas.find_withtag("all")[0],
            width=event.width
        )

    def _bind_mousewheel_to_widget(self, widget):
        """将鼠标滚轮事件绑定到指定组件"""
        widget.bind("<MouseWheel>", self._on_mousewheel)
        widget.bind("<Button-4>", self._on_mousewheel)  # Linux
        widget.bind("<Button-5>", self._on_mousewheel)  # Linux

    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        # 检查是否需要滚动（只有当内容超出可视区域时才滚动）
        if self.files_canvas.winfo_exists():
            # 获取滚动区域信息
            scrollregion = self.files_canvas.cget("scrollregion")
            if scrollregion:
                # 解析滚动区域
                _, y1, _, y2 = map(float, scrollregion.split())
                canvas_height = self.files_canvas.winfo_height()
                content_height = y2 - y1

                # 只有当内容高度大于画布高度时才允许滚动
                if content_height <= canvas_height:
                    return

        # Windows和MacOS
        if event.delta:
            delta = int(-1 * (event.delta / 120))
        # Linux
        elif event.num == 4:
            delta = -1
        elif event.num == 5:
            delta = 1
        else:
            delta = 0

        self.files_canvas.yview_scroll(delta, "units")

    def open_file_preview(self, file_path):
        """打开文件预览窗口"""
        try:
            # 创建预览窗口
            preview_window = tk.Toplevel(self.parent)
            preview_window.title(f"文件预览 - {os.path.basename(file_path)}")
            preview_window.geometry("800x650")
            preview_window.configure(bg=self.bg_color)

            # 创建主框架
            main_frame = tk.Frame(preview_window, bg=self.bg_color, padx=10, pady=10)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 文件信息
            info_frame = tk.Frame(main_frame, bg=self.panel_color, padx=10, pady=5)
            info_frame.pack(fill=tk.X, pady=(0, 10))

            # 文件路径
            path_label = tk.Label(
                info_frame,
                text=f"文件路径: {file_path}",
                font=("微软雅黑", 9),
                fg=self.text_color,
                bg=self.panel_color,
                anchor="w"
            )
            path_label.pack(fill=tk.X)

            # 文件大小和修改时间
            try:
                stat = os.stat(file_path)
                size = stat.st_size
                modified = datetime.datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M:%S")

                details_label = tk.Label(
                    info_frame,
                    text=f"大小: {size:,} 字节 | 修改时间: {modified}",
                    font=("微软雅黑", 9),
                    fg="#6c757d",
                    bg=self.panel_color,
                    anchor="w"
                )
                details_label.pack(fill=tk.X)
            except:
                pass

            # 文本内容区域
            text_frame = tk.Frame(main_frame, bg=self.panel_color)
            text_frame.pack(fill=tk.BOTH, expand=True)

            # 创建文本框和滚动条
            text_scrollbar = tk.Scrollbar(text_frame)
            text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            text_widget = tk.Text(
                text_frame,
                wrap=tk.WORD,
                font=("微软雅黑", 12),
                bg="white",
                fg=self.text_color,
                yscrollcommand=text_scrollbar.set,
                padx=10,
                pady=10
            )
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            text_scrollbar.config(command=text_widget.yview)

            # 读取并显示文件内容
            try:
                content = self._read_file_with_encoding_detection(file_path)

                text_widget.insert(tk.END, content)
                text_widget.config(state=tk.DISABLED)  # 设为只读

                # 显示字符统计
                char_count = len(content)
                line_count = content.count('\n') + 1

                stats_label = tk.Label(
                    info_frame,
                    text=f"字符数: {char_count:,} | 行数: {line_count:,}",
                    font=("微软雅黑", 9),
                    fg="#007bff",
                    bg=self.panel_color,
                    anchor="w"
                )
                stats_label.pack(fill=tk.X)

            except Exception as e:
                text_widget.insert(tk.END, f"无法读取文件内容: {str(e)}")
                text_widget.config(state=tk.DISABLED)

            # 按钮区域
            button_frame = tk.Frame(main_frame, bg=self.bg_color)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            # 关闭按钮
            close_btn = tk.Button(
                button_frame,
                text="关闭",
                font=("微软雅黑", 10),
                bg="#6c757d",
                fg="white",
                padx=20,
                pady=5,
                relief=tk.FLAT,
                cursor="hand2",
                command=preview_window.destroy
            )
            close_btn.pack(side=tk.RIGHT)

            # 在文件管理器中显示按钮
            show_in_explorer_btn = tk.Button(
                button_frame,
                text="在文件管理器中显示",
                font=("微软雅黑", 10),
                bg=self.accent_color,
                fg="white",
                padx=20,
                pady=5,
                relief=tk.FLAT,
                cursor="hand2",
                command=lambda: self.show_in_explorer(file_path)
            )
            show_in_explorer_btn.pack(side=tk.RIGHT, padx=(0, 10))

        except Exception as e:
            messagebox.showerror("预览错误", f"无法打开文件预览: {str(e)}")

    def show_in_explorer(self, file_path):
        """在文件管理器中显示文件"""
        try:
            import subprocess
            subprocess.run(['explorer', '/select,', file_path])
        except Exception as e:
            messagebox.showerror("错误", f"无法在文件管理器中显示文件: {str(e)}")

    def get_file_info(self, file_path):
        """获取文件的信息：字数、预计配音时长和修改时间"""
        try:
            # 获取文件修改时间
            mod_time = os.path.getmtime(file_path)
            mod_time_str = datetime.datetime.fromtimestamp(mod_time).strftime("%Y-%m-%d %H:%M")
            
            # 获取文件内容和字数
            content = self._read_file_with_encoding_detection(file_path)
            char_count = len(content)
            
            # 计算预计配音时长（假设20万字约10小时）
            # 即每小时约2万字，每分钟约333字
            duration_hours = char_count / 20000  # 小时
            
            # 格式化显示
            if duration_hours < 1:
                duration_str = f"{int(duration_hours * 60)}分钟"
            else:
                hours = int(duration_hours)
                minutes = int((duration_hours - hours) * 60)
                duration_str = f"{hours}小时{minutes}分钟"
            
            return {
                "char_count": char_count,
                "char_count_str": f"{char_count:,}字",
                "duration": duration_str,
                "modified": mod_time_str
            }
        except Exception as e:
            return {
                "char_count": 0,
                "char_count_str": "读取错误",
                "duration": "未知",
                "modified": "未知"
            }

    def _read_file_with_encoding_detection(self, file_path):
        """
        智能读取文件，使用编码检测

        参数:
            file_path (str): 文件路径

        返回:
            str: 文件内容
        """
        try:
            # 首先尝试使用chardet检测编码
            encoding_info = self._detect_file_encoding(file_path)
            detected_encoding = encoding_info['encoding']
            confidence = encoding_info['confidence']

            if detected_encoding and confidence > 0.7:
                # 如果检测到的编码置信度较高，使用检测到的编码
                try:
                    with open(file_path, 'r', encoding=detected_encoding) as f:
                        content = f.read()
                    return content

                except Exception as e:
                    print(f"使用检测到的编码 {detected_encoding} 读取失败: {e}")

            # 如果chardet检测失败或置信度低，尝试常用编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'utf-16', 'latin-1', 'cp1252']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()

                    # 简单验证内容是否正常（检查是否有过多的替换字符）
                    if self._validate_content(content):
                        return content

                except Exception:
                    continue

            # 如果所有编码都失败，使用二进制模式读取
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            # 尝试用utf-8解码，替换无法解码的字符
            content = raw_data.decode('utf-8', errors='replace')
            return content

        except Exception as e:
            raise Exception(f"无法读取文件: {str(e)}")

    def _detect_file_encoding(self, file_path):
        """
        使用chardet检测文件编码

        参数:
            file_path (str): 文件路径

        返回:
            dict: 包含编码信息的字典
        """
        try:
            # 尝试导入chardet
            import chardet
        except ImportError:
            # 如果没有安装chardet，返回默认值
            return {'encoding': None, 'confidence': 0}

        try:
            # 读取文件的一部分来检测编码
            with open(file_path, 'rb') as f:
                # 读取前10KB来检测编码，对于大文件这样更高效
                raw_data = f.read(10240)
                if len(raw_data) < 10240:
                    # 如果文件小于10KB，读取全部
                    f.seek(0)
                    raw_data = f.read()

            # 使用chardet检测编码
            result = chardet.detect(raw_data)
            return result

        except Exception as e:
            print(f"编码检测失败: {e}")
            return {'encoding': None, 'confidence': 0}

    def _validate_content(self, content):
        """
        验证文件内容是否正常

        参数:
            content (str): 文件内容

        返回:
            bool: 内容是否正常
        """
        if not content:
            return True

        # 检查替换字符的比例
        replacement_chars = content.count('�')
        total_chars = len(content)

        if total_chars == 0:
            return True

        # 如果替换字符超过5%，认为编码可能不正确
        replacement_ratio = replacement_chars / total_chars
        return replacement_ratio < 0.05

    def import_files(self):
        """导入多个文件"""
        file_paths = filedialog.askopenfilenames(
            title="选择文本文件",
            filetypes=[
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_paths:
            self.add_files(list(file_paths))
    
    def import_folder(self):
        """导入文件夹中的所有文本文件"""
        folder_path = filedialog.askdirectory(title="选择文件夹")
        
        if folder_path:
            txt_files = []
            
            # 遍历文件夹中的文件
            for root, _, files in os.walk(folder_path):
                for file in files:
                    if file.lower().endswith('.txt'):
                        txt_files.append(os.path.join(root, file))
            
            if txt_files:
                self.add_files(txt_files)
            else:
                messagebox.showinfo("提示", "所选文件夹中没有找到 .txt 文件")
    
    def add_files(self, new_file_paths):
        """添加文件到列表"""
        # 检查文件是否已经在列表中
        added_count = 0
        
        # 显示进度对话框
        progress = tk.Toplevel(self.parent)
        progress.title("正在加载文件")
        progress.geometry("300x100")
        progress.transient(self.parent)
        progress.grab_set()
        
        # 进度标签
        progress_label = tk.Label(
            progress, 
            text="正在读取文件信息...",
            font=("微软雅黑", 10),
            pady=10
        )
        progress_label.pack()
        
        # 进度条
        progress_bar = ttk.Progressbar(
            progress,
            orient="horizontal",
            length=250,
            mode="determinate"
        )
        progress_bar.pack(pady=10)
        
        # 更新进度的函数
        def update_progress(current, total):
            progress_bar["value"] = (current / total) * 100
            progress_label.config(text=f"正在读取文件信息... ({current}/{total})")
            progress.update()
        
        # 处理文件
        for i, file_path in enumerate(new_file_paths):
            if file_path not in self.file_paths:
                # 更新进度
                update_progress(i + 1, len(new_file_paths))
                
                # 添加到文件路径列表
                self.file_paths.append(file_path)
                
                # 创建复选框变量
                var = tk.BooleanVar(value=True)
                self.file_vars.append(var)
                
                # 获取文件信息
                file_info = self.get_file_info(file_path)
                
                # 创建文件行（使用交替背景色）
                row_bg = "#f8f9fa" if len(self.file_paths) % 2 == 0 else "#ffffff"
                file_row = tk.Frame(self.files_inner_frame, bg=row_bg, relief=tk.FLAT, bd=0)
                file_row.pack(fill=tk.X, pady=0)

                # 设置列权重
                file_row.columnconfigure(1, weight=1)

                # 复选框
                checkbox = tk.Checkbutton(
                    file_row,
                    variable=var,
                    bg=row_bg,
                    width=3,
                    relief=tk.FLAT,
                    font=("微软雅黑", 9)
                )
                checkbox.grid(row=0, column=0, padx=8, pady=8)

                # 文件名（可双击预览）
                filename_label = tk.Label(
                    file_row,
                    text=os.path.basename(file_path),
                    anchor=tk.W,
                    bg=row_bg,
                    width=25,
                    cursor="hand2",
                    fg="#007bff",  # 蓝色表示可点击
                    font=("微软雅黑", 9),
                    relief=tk.FLAT
                )
                filename_label.grid(row=0, column=1, sticky="ew", padx=8, pady=8)

                # 绑定双击事件和悬停效果
                filename_label.bind("<Double-Button-1>", lambda e, path=file_path: self.open_file_preview(path))
                filename_label.bind("<Enter>", lambda e: filename_label.config(fg="#0056b3"))  # 悬停时颜色变深
                filename_label.bind("<Leave>", lambda e: filename_label.config(fg="#007bff"))  # 离开时恢复

                # 字数
                size_label = tk.Label(
                    file_row,
                    text=file_info["char_count_str"],
                    anchor=tk.CENTER,
                    bg=row_bg,
                    width=10,
                    font=("微软雅黑", 9),
                    fg=self.text_color,
                    relief=tk.FLAT
                )
                size_label.grid(row=0, column=2, padx=8, pady=8)

                # 预计时长
                duration_label = tk.Label(
                    file_row,
                    text=file_info["duration"],
                    anchor=tk.CENTER,
                    bg=row_bg,
                    width=12,
                    font=("微软雅黑", 9),
                    fg=self.text_color,
                    relief=tk.FLAT
                )
                duration_label.grid(row=0, column=3, padx=8, pady=8)

                # 修改时间
                modified_label = tk.Label(
                    file_row,
                    text=file_info["modified"],
                    anchor=tk.CENTER,
                    bg=row_bg,
                    width=15,
                    font=("微软雅黑", 9),
                    fg="#6c757d",  # 灰色表示次要信息
                    relief=tk.FLAT
                )
                modified_label.grid(row=0, column=4, padx=8, pady=8)

                # 为所有文件行组件绑定鼠标滚轮事件
                for widget in [file_row, checkbox, filename_label, size_label, duration_label, modified_label]:
                    self._bind_mousewheel_to_widget(widget)
                
                added_count += 1
        
        # 关闭进度对话框
        progress.destroy()
        
        # 更新状态
        self.status_label.config(text=f"已添加 {added_count} 个文件，总计 {len(self.file_paths)} 个文件")
    
    def select_all_files(self):
        """选中所有文件"""
        for var in self.file_vars:
            var.set(True)
    
    def deselect_all_files(self):
        """取消选中所有文件"""
        for var in self.file_vars:
            var.set(False)
    
    def delete_selected_files(self):
        """删除选中的文件"""
        # 获取选中的索引
        selected_indices = [i for i, var in enumerate(self.file_vars) if var.get()]
        
        if not selected_indices:
            messagebox.showinfo("提示", "请先选择要删除的文件")
            return
        
        # 确认删除
        confirm = messagebox.askyesno(
            "确认删除",
            f"确定要从列表中删除选中的 {len(selected_indices)} 个文件吗？\n(注意：这只会从列表中移除，不会删除磁盘上的文件)"
        )
        
        if not confirm:
            return
        
        # 从后往前删除，避免索引变化
        for i in sorted(selected_indices, reverse=True):
            # 移除文件路径
            self.file_paths.pop(i)
            # 移除变量
            self.file_vars.pop(i)
            # 删除行（销毁对应的Frame）
            self.files_inner_frame.winfo_children()[i].destroy()
        
        # 更新状态
        self.status_label.config(text=f"已删除 {len(selected_indices)} 个文件，剩余 {len(self.file_paths)} 个文件")
    
    def get_selected_file_paths(self):
        """获取选中的文件路径"""
        selected_paths = [
            self.file_paths[i] for i, var in enumerate(self.file_vars) if var.get()
        ]
        return selected_paths
    
    def process_selected_files(self):
        """处理选中的文件"""
        selected_paths = self.get_selected_file_paths()
        
        if not selected_paths:
            messagebox.showinfo("提示", "请先选择要处理的文件")
            return
        
        # 获取处理选项
        format_text = self.format_var.get()
        split_text = self.split_var.get()
        
        if not format_text and not split_text:
            messagebox.showinfo("提示", "请至少选择一种处理方式（格式化或分割）")
            return
        
        # 获取分割参数
        try:
            first_file_limit = int(self.first_limit_var.get())
            other_file_limit = int(self.other_limit_var.get())
            
            if first_file_limit <= 0 or other_file_limit <= 0:
                messagebox.showerror("错误", "字数限制必须为正整数")
                return
        except ValueError:
            messagebox.showerror("错误", "字数限制必须为整数")
            return
        
        # 在后台线程中处理文件
        self.status_label.config(text=f"正在处理 {len(selected_paths)} 个文件...")
        
        # 禁用按钮
        self.import_files_btn.config(state=tk.DISABLED)
        self.import_folder_btn.config(state=tk.DISABLED)
        
        # 创建并启动处理线程
        processing_thread = threading.Thread(
            target=self._process_files_thread,
            args=(selected_paths, format_text, split_text, first_file_limit, other_file_limit)
        )
        processing_thread.daemon = True
        processing_thread.start()
    
    def _process_files_thread(self, file_paths, format_text, split_text, first_file_limit, other_file_limit):
        """在后台线程中处理文件"""
        try:
            # 处理文件
            results = self.processor.process_files(
                file_paths, format_text, split_text, first_file_limit, other_file_limit
            )
            
            # 统计处理结果并收集生成的文件
            formatted_count = 0
            split_count = 0
            error_count = 0
            generated_files = []

            for result in results:
                if 'error' in result:
                    error_count += 1
                else:
                    if result['formatted_file']:
                        formatted_count += 1
                        generated_files.append(result['formatted_file'])
                    if result['split_files']:
                        split_count += len(result['split_files'])
                        generated_files.extend(result['split_files'])

            # 更新UI（在主线程中）
            self.parent.winfo_toplevel().after(0, lambda: self._update_after_processing(
                formatted_count, split_count, error_count, generated_files
            ))
        
        except Exception as e:
            # 更新UI（在主线程中）
            self.parent.winfo_toplevel().after(0, lambda: self._show_error(str(e)))
    
    def _update_after_processing(self, formatted_count, split_count, error_count, generated_files):
        """处理完成后更新UI"""
        status_text = []

        if formatted_count > 0:
            status_text.append(f"已格式化 {formatted_count} 个文件")

        if split_count > 0:
            status_text.append(f"已分割生成 {split_count} 个文件")

        if error_count > 0:
            status_text.append(f"处理失败 {error_count} 个文件")

        if status_text:
            self.status_label.config(text=", ".join(status_text))
        else:
            self.status_label.config(text="处理完成，但无文件被处理")

        # 启用按钮
        self.import_files_btn.config(state=tk.NORMAL)
        self.import_folder_btn.config(state=tk.NORMAL)

        # 自动添加生成的文件到列表
        if generated_files:
            # 询问用户是否要将生成的文件添加到列表中
            result = messagebox.askyesno(
                "处理完成",
                f"{', '.join(status_text)}\n\n是否将生成的 {len(generated_files)} 个文件添加到文件列表中？"
            )

            if result:
                # 添加生成的文件到列表
                self.add_files(generated_files)
                self.status_label.config(text=f"{', '.join(status_text)}，已添加到文件列表")
        else:
            # 显示完成消息
            messagebox.showinfo("处理完成", ", ".join(status_text) if status_text else "处理完成")
    
    def _show_error(self, error_message):
        """显示错误消息"""
        self.status_label.config(text=f"处理错误: {error_message}")
        
        # 启用按钮
        self.import_files_btn.config(state=tk.NORMAL)
        self.import_folder_btn.config(state=tk.NORMAL)
        
        # 显示错误消息
        messagebox.showerror("处理错误", error_message) 