import os
import re

class TextProcessor:
    """文本处理核心类，提供文本格式化和分割功能"""
    
    def __init__(self):
        # 可配置的标题正则表达式
        self.title_patterns = [
            r"^第[一二三四五六七八九十百千万\d]+章.*$",  # 匹配"第x章"格式（包括阿拉伯数字）
            r"^第[一二三四五六七八九十百千万\d]+节.*$",  # 匹配"第x节"格式
            r"^第[一二三四五六七八九十百千万\d]+回.*$",  # 匹配"第x回"格式
            r"^第[一二三四五六七八九十百千万\d]+部.*$",  # 匹配"第x部"格式
            r"^第[一二三四五六七八九十百千万\d]+卷.*$",  # 匹配"第x卷"格式
            r"^Chapter\s+\d+.*$",  # 匹配英文章节标题
            r"^\d+[\.、]\s*.*$",   # 匹配数字开头的标题
            r"^[一二三四五六七八九十百千万\d]+[\.、]\s*.*$",  # 匹配中文数字开头的标题
            r"^序章.*$",           # 匹配序章
            r"^楔子.*$",           # 匹配楔子
            r"^尾声.*$",           # 匹配尾声
            r"^后记.*$",           # 匹配后记
            r"^前言.*$",           # 匹配前言
            r"^引子.*$"            # 匹配引子
        ]

        # 版权声明和无用信息的模式
        self.useless_patterns = [
            r".*来源来自网络.*",
            r".*请于下载后.*小时内删除.*",
            r".*内容版权归作者所有.*",
            r".*如不慎该文本侵犯了您的权益.*",
            r".*请麻烦通知我们及时删除.*",
            r".*谢谢.*",
            r"^[\s]*$",  # 空行
            r".*版权所有.*",
            r".*盗版必究.*",
            r".*转载请注明.*",
            r".*本书来源.*",
            r".*免费下载.*"
        ]
    
    def format_text(self, text):
        """
        格式化文本：
        1. 在标点符号处另起一行并删除标点符号
        2. 删除标题行

        参数:
            text (str): 输入文本

        返回:
            str: 格式化后的文本
        """
        # 按行分割文本
        lines = text.split('\n')
        formatted_lines = []

        # 处理每一行
        for line in lines:
            original_line = line.strip()
            if not original_line:
                continue

            # 第一步：在标点符号处分割并删除标点符号
            # 定义需要分割的标点符号（句子结束符号）
            sentence_endings = r'[。！？.!?；;]'
            # 定义其他标点符号（逗号等）
            other_punctuation = r'[，,、：:]'

            # 先处理句子结束符号 - 在这些符号处分割并删除符号
            segments = re.split(sentence_endings, original_line)

            # 处理每个分割后的段落
            processed_segments = []
            for segment in segments:
                if not segment.strip():
                    continue

                # 处理其他标点符号 - 在逗号等处也分割
                sub_segments = re.split(other_punctuation, segment)
                for sub_segment in sub_segments:
                    if not sub_segment.strip():
                        continue

                    # 删除剩余的标点符号和特殊符号
                    cleaned_segment = re.sub(r'[【】〖〗《》「」『』""''（）()[\]{}~～·•※]', '', sub_segment)
                    # 删除其他特殊符号，但保留中英文字符、数字和空格
                    cleaned_segment = re.sub(r'[^\w\s\u4e00-\u9fff]', '', cleaned_segment)

                    if cleaned_segment.strip():
                        processed_segments.append(cleaned_segment.strip())

            # 第二步：检查每个处理后的段落是否为标题行
            for segment in processed_segments:
                # 检查是否为标题行
                if any(re.match(pattern, segment.strip()) for pattern in self.title_patterns):
                    continue  # 跳过标题行

                # 如果不是标题行且不为空，添加到结果中
                if segment.strip():
                    formatted_lines.append(segment.strip())

        # 合并处理后的行
        return '\n'.join(formatted_lines)
    
    def split_text(self, text, first_file_limit=150000, other_file_limit=200000):
        """
        将文本分割成多个文件
        
        参数:
            text (str): 输入文本
            first_file_limit (int): 第一个文件的字符数上限
            other_file_limit (int): 其他文件的字符数上限
            
        返回:
            list: 文本片段列表
        """
        # 文本总长度
        total_length = len(text)
        
        # 分割结果
        text_chunks = []
        
        # 当前位置和当前文件大小限制
        current_pos = 0
        current_limit = first_file_limit
        
        while current_pos < total_length:
            # 计算当前块的结束位置
            end_pos = min(current_pos + current_limit, total_length)
            
            # 如果不是最后一块，尝试在句子末尾分割
            if end_pos < total_length:
                # 寻找句子结束的位置（中英文句号）
                sentence_end = text.rfind('。', current_pos, end_pos)
                if sentence_end == -1:
                    sentence_end = text.rfind('.', current_pos, end_pos)
                
                # 如果找到合适的分割点，调整结束位置
                if sentence_end != -1 and sentence_end > current_pos:
                    end_pos = sentence_end + 1
            
            # 提取当前文本块
            chunk = text[current_pos:end_pos]
            text_chunks.append(chunk)
            
            # 更新当前位置
            current_pos = end_pos
            
            # 后续文件使用other_file_limit
            current_limit = other_file_limit
        
        return text_chunks
    
    def process_file(self, file_path, format_text=False, split_text=False, 
                    first_file_limit=150000, other_file_limit=200000):
        """
        处理单个文件
        
        参数:
            file_path (str): 文件路径
            format_text (bool): 是否格式化文本
            split_text (bool): 是否分割文本
            first_file_limit (int): 第一个文件的字符数上限
            other_file_limit (int): 其他文件的字符数上限
            
        返回:
            dict: 处理结果，包括生成的文件路径
        """
        result = {
            'original_file': file_path,
            'formatted_file': None,
            'split_files': []
        }
        
        try:
            # 读取文件内容
            content = self._read_file_with_encoding_detection(file_path)
            
            # 获取基本文件信息
            file_dir = os.path.dirname(file_path)
            file_name = os.path.basename(file_path)
            file_base, file_ext = os.path.splitext(file_name)
            
            # 格式化文本
            if format_text:
                formatted_content = self.format_text(content)
                formatted_file = os.path.join(file_dir, f"{file_base}_格式化{file_ext}")
                
                with open(formatted_file, 'w', encoding='utf-8') as f:
                    f.write(formatted_content)
                
                result['formatted_file'] = formatted_file
                
                # 如果需要分割，使用格式化后的内容
                if split_text:
                    content = formatted_content
            
            # 分割文本
            if split_text:
                chunks = self.split_text(content, first_file_limit, other_file_limit)
                
                # 保存分割后的文件
                for i, chunk in enumerate(chunks):
                    split_file = os.path.join(file_dir, f"{file_base}_{i+1}{file_ext}")

                    with open(split_file, 'w', encoding='utf-8') as f:
                        f.write(chunk)

                    result['split_files'].append(split_file)
            
            return result
        
        except Exception as e:
            raise Exception(f"处理文件 {file_path} 时出错: {str(e)}")

    def _read_file_with_encoding_detection(self, file_path):
        """
        智能读取文件，使用编码检测

        参数:
            file_path (str): 文件路径

        返回:
            str: 文件内容
        """
        try:
            # 首先尝试使用chardet检测编码
            encoding_info = self._detect_file_encoding(file_path)
            detected_encoding = encoding_info['encoding']
            confidence = encoding_info['confidence']

            if detected_encoding and confidence > 0.7:
                # 如果检测到的编码置信度较高，使用检测到的编码
                try:
                    with open(file_path, 'r', encoding=detected_encoding) as f:
                        content = f.read()
                    return content

                except Exception as e:
                    print(f"使用检测到的编码 {detected_encoding} 读取失败: {e}")

            # 如果chardet检测失败或置信度低，尝试常用编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'utf-16', 'latin-1', 'cp1252']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()

                    # 简单验证内容是否正常（检查是否有过多的替换字符）
                    if self._validate_content(content):
                        return content

                except Exception:
                    continue

            # 如果所有编码都失败，使用二进制模式读取
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            # 尝试用utf-8解码，替换无法解码的字符
            content = raw_data.decode('utf-8', errors='replace')
            return content

        except Exception as e:
            raise Exception(f"无法读取文件: {str(e)}")

    def _detect_file_encoding(self, file_path):
        """
        使用chardet检测文件编码

        参数:
            file_path (str): 文件路径

        返回:
            dict: 包含编码信息的字典
        """
        try:
            # 尝试导入chardet
            import chardet
        except ImportError:
            # 如果没有安装chardet，返回默认值
            return {'encoding': None, 'confidence': 0}

        try:
            # 读取文件的一部分来检测编码
            with open(file_path, 'rb') as f:
                # 读取前10KB来检测编码，对于大文件这样更高效
                raw_data = f.read(10240)
                if len(raw_data) < 10240:
                    # 如果文件小于10KB，读取全部
                    f.seek(0)
                    raw_data = f.read()

            # 使用chardet检测编码
            result = chardet.detect(raw_data)
            return result

        except Exception as e:
            print(f"编码检测失败: {e}")
            return {'encoding': None, 'confidence': 0}

    def _validate_content(self, content):
        """
        验证文件内容是否正常

        参数:
            content (str): 文件内容

        返回:
            bool: 内容是否正常
        """
        if not content:
            return True

        # 检查替换字符的比例
        replacement_chars = content.count('�')
        total_chars = len(content)

        if total_chars == 0:
            return True

        # 如果替换字符超过5%，认为编码可能不正确
        replacement_ratio = replacement_chars / total_chars
        return replacement_ratio < 0.05

    def process_files(self, file_paths, format_text=False, split_text=False,
                     first_file_limit=150000, other_file_limit=200000):
        """
        批量处理多个文件
        
        参数:
            file_paths (list): 文件路径列表
            format_text (bool): 是否格式化文本
            split_text (bool): 是否分割文本
            first_file_limit (int): 第一个文件的字符数上限
            other_file_limit (int): 其他文件的字符数上限
            
        返回:
            list: 处理结果列表
        """
        results = []
        
        for file_path in file_paths:
            try:
                result = self.process_file(file_path, format_text, split_text, 
                                          first_file_limit, other_file_limit)
                results.append(result)
            except Exception as e:
                results.append({
                    'original_file': file_path,
                    'error': str(e)
                })
        
        return results 