# 视频合成循环视频编码优化

## 优化概述

针对批量生成不同时长循环视频的场景，实现了"先编码后循环"的优化策略，大幅提升处理速度。

## 优化前的问题

### 原始逻辑
1. 对于每个不同时长的音频文件
2. 都会调用 `loop_video_to_duration` 生成对应时长的循环视频
3. 每次都会对整个循环视频进行重新编码（应用分辨率、码率等设置）
4. 导致大量重复的编码计算

### 性能问题
- **时间复杂度**: O(n × duration × encoding_time)
- **CPU/GPU负担**: 每个循环视频都要完整编码
- **重复计算**: 相同的编码设置被重复应用多次

## 优化后的方案

### 新的逻辑
1. **检查编码需求**: 判断是否需要应用编码设置（分辨率、码率等）
2. **预编码基础视频**: 如果需要编码，先对原视频应用一次编码设置
3. **快速循环拼接**: 使用预编码的视频进行简单的文件拼接

### 性能提升
- **时间复杂度**: O(1 × encoding_time) + O(n × copy_time)
- **编码次数**: 从 n 次减少到 1 次
- **后续操作**: 只是简单的文件拼接，速度极快

## 技术实现

### 核心方法

#### 1. `_should_use_pre_encoding(settings)`
检查是否应该使用预编码优化：
- 自定义分辨率 (`custom_resolution`)
- 自定义压缩 (`custom_compression`) 
- 强制编码 (`force_encoding`)

#### 2. `_pre_encode_base_video(loop_video_path, settings)`
预编码基础视频：
- 对原视频应用所有编码设置
- 生成编码后的基础视频文件
- 支持缓存机制，避免重复编码

#### 3. `_loop_pre_encoded_video(pre_encoded_video, target_duration, output_path)`
使用预编码视频快速循环：
- 使用 FFmpeg 的 concat demuxer
- 采用 `-c copy` 模式，不重新编码
- 只进行文件拼接和时长裁剪

### 缓存机制

#### 预编码视频缓存
- **缓存键**: `base_{video_id}_{settings_hash}`
- **缓存位置**: 临时目录/缓存目录
- **缓存策略**: 基于视频文件和编码设置的组合

#### 循环视频缓存
- 保持原有的循环视频缓存机制
- 支持预编码和原始两种生成方式的缓存

## 使用场景

### 适用情况
✅ 需要应用编码设置（分辨率、码率等）  
✅ 批量生成多个不同时长的循环视频  
✅ 原视频文件较大或编码设置复杂  
✅ 对处理速度有较高要求  

### 不适用情况
❌ 不需要任何编码设置（直接使用原视频）  
❌ 只生成单个循环视频  
❌ 原视频文件很小且编码很快  

## 性能对比

### 示例场景
- 原视频: 10秒，1080p
- 目标: 生成 30秒、45秒、60秒 三个循环视频
- 设置: 720p + 2000k码率

### 优化前
```
视频1 (30秒): 编码30秒视频 → 耗时 ~15秒
视频2 (45秒): 编码45秒视频 → 耗时 ~22秒  
视频3 (60秒): 编码60秒视频 → 耗时 ~30秒
总耗时: ~67秒
```

### 优化后
```
预编码: 编码10秒基础视频 → 耗时 ~5秒
视频1 (30秒): 拼接3次 → 耗时 ~2秒
视频2 (45秒): 拼接4.5次 → 耗时 ~3秒
视频3 (60秒): 拼接6次 → 耗时 ~4秒
总耗时: ~14秒
```

### 性能提升
- **速度提升**: 约 4.8倍
- **编码次数**: 从 3次 减少到 1次
- **总编码时长**: 从 135秒 减少到 10秒

## 时间戳处理

### 关键参数
- `-avoid_negative_ts make_zero`: 避免负时间戳
- `-fflags +genpts+igndts`: 重新生成时间戳并忽略DTS
- `-vsync cfr`: 恒定帧率
- `-async 1`: 音频同步

### 确保连续性
- 使用 concat demuxer 确保时间戳连续
- 精确的时长控制避免跳转问题
- 关键帧间隔设置保证播放流畅

## 向后兼容

### 回退机制
- 如果预编码失败，自动回退到原始逻辑
- 如果快速循环失败，可以回退到重新编码
- 保持所有原有API接口不变

### 渐进式启用
- 只在检测到编码需求时才启用优化
- 不影响现有的简单循环场景
- 用户无需修改调用代码

## 总结

这个优化实现了"编码一次，循环多次"的策略，在需要应用编码设置的批量循环视频生成场景中，可以获得显著的性能提升，同时保证视频质量和时间戳的正确性。
