"""
路径工具模块
提供兼容开发环境和打包后环境的路径获取功能
"""

import os
import sys

def get_app_path():
    """
    获取应用程序的根目录路径
    兼容开发环境、PyInstaller打包和Nuitka打包环境

    返回:
        str: 应用程序根目录的绝对路径
    """
    # 检查是否为Nuitka打包
    is_nuitka_packed = not sys.argv[0].endswith('.py')

    if getattr(sys, 'frozen', False):
        # PyInstaller打包环境
        # sys.executable 是exe文件的路径
        return os.path.dirname(sys.executable)
    elif is_nuitka_packed:
        # Nuitka打包环境
        # sys.argv[0] 是exe文件的路径
        return os.path.dirname(sys.argv[0])
    else:
        # 开发环境，返回脚本所在的根目录
        # 从common目录向上一级到达根目录
        return os.path.dirname(os.path.dirname(__file__))

def get_config_path(config_file="config.json"):
    """
    获取配置文件的完整路径
    
    参数:
        config_file (str): 配置文件名
        
    返回:
        str: 配置文件的完整路径
    """
    return os.path.join(get_app_path(), config_file)

def get_config_dir_path():
    """
    获取config目录的完整路径
    
    返回:
        str: config目录的完整路径
    """
    return os.path.join(get_app_path(), "config")

def get_output_path(output_dir="output"):
    """
    获取输出目录的完整路径
    
    参数:
        output_dir (str): 输出目录名
        
    返回:
        str: 输出目录的完整路径
    """
    return os.path.join(get_app_path(), output_dir)

def ensure_dir_exists(dir_path):
    """
    确保目录存在，如果不存在则创建
    
    参数:
        dir_path (str): 目录路径
        
    返回:
        bool: 是否成功创建或目录已存在
    """
    try:
        os.makedirs(dir_path, exist_ok=True)
        return True
    except Exception as e:
        print(f"创建目录失败: {dir_path}, 错误: {e}")
        return False

def get_resource_path(relative_path):
    """
    获取资源文件的路径
    兼容开发环境、PyInstaller打包和Nuitka打包环境

    参数:
        relative_path (str): 相对于应用根目录的路径

    返回:
        str: 资源文件的完整路径
    """
    # 检查是否为Nuitka打包
    is_nuitka_packed = not sys.argv[0].endswith('.py')

    if getattr(sys, 'frozen', False):
        # PyInstaller打包后的环境
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller 临时目录
            base_path = sys._MEIPASS
        else:
            # 其他PyInstaller情况
            base_path = os.path.dirname(sys.executable)
    elif is_nuitka_packed:
        # Nuitka打包环境
        # 对于Nuitka，资源文件通常在exe同目录下
        base_path = os.path.dirname(sys.argv[0])
    else:
        # 开发环境
        base_path = get_app_path()

    return os.path.join(base_path, relative_path)

def detect_ffmpeg_path():
    """
    自动检测FFmpeg路径
    优先检查应用程序根目录下的ffmpeg文件夹，然后检查系统PATH

    返回:
        str: FFmpeg bin目录路径，如果未找到则返回None
    """
    # 1. 检查应用程序根目录下的ffmpeg/bin目录
    app_path = get_app_path()
    ffmpeg_bin_path = os.path.join(app_path, "ffmpeg", "bin")

    if os.path.exists(ffmpeg_bin_path):
        ffmpeg_exe = os.path.join(ffmpeg_bin_path, "ffmpeg.exe")
        ffprobe_exe = os.path.join(ffmpeg_bin_path, "ffprobe.exe")

        if os.path.exists(ffmpeg_exe) and os.path.exists(ffprobe_exe):
            return ffmpeg_bin_path

    # 2. 检查系统PATH中是否有ffmpeg
    try:
        import shutil
        ffmpeg_path = shutil.which("ffmpeg")
        ffprobe_path = shutil.which("ffprobe")

        if ffmpeg_path and ffprobe_path:
            # 返回ffmpeg所在的目录
            return os.path.dirname(ffmpeg_path)
    except Exception:
        pass

    return None
