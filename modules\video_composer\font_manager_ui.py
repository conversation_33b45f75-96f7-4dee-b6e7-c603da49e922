"""
字体管理器UI界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
from PIL import Image, ImageTk
import threading
import os


class FontManagerWindow:
    def __init__(self, parent, font_manager):
        """
        初始化字体管理窗口
        
        参数:
            parent: 父窗口
            font_manager: 字体管理器实例
        """
        self.parent = parent
        self.font_manager = font_manager
        
        # 创建窗口
        self.window = tk.Toplevel(parent)
        self.window.title("字体管理器")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            self.window.iconbitmap("icon.ico")
        except:
            pass
        
        self.setup_ui()
        self.load_fonts()
        
        # 居中显示窗口
        self.center_window()
    
    def center_window(self):
        """居中显示窗口"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """设置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="字体管理器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 左侧：字体列表
        left_frame = ttk.LabelFrame(main_frame, text="可用字体", padding="5")
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(1, weight=1)
        
        # 搜索框
        search_frame = ttk.Frame(left_frame)
        search_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        search_frame.columnconfigure(1, weight=1)
        
        ttk.Label(search_frame, text="搜索:").grid(row=0, column=0, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_changed)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # 字体列表
        list_frame = ttk.Frame(left_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        self.font_listbox = tk.Listbox(list_frame, selectmode=tk.SINGLE)
        self.font_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.font_listbox.bind('<<ListboxSelect>>', self.on_font_selected)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.font_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.font_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 按钮框架
        button_frame = ttk.Frame(left_frame)
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Button(button_frame, text="刷新字体", command=self.refresh_fonts).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="添加字体", command=self.add_font).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="删除字体", command=self.remove_font).pack(side=tk.LEFT)
        
        # 右侧：字体预览和设置
        right_frame = ttk.LabelFrame(main_frame, text="字体预览与设置", padding="5")
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # 预览文本设置
        preview_settings_frame = ttk.Frame(right_frame)
        preview_settings_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        preview_settings_frame.columnconfigure(1, weight=1)
        
        ttk.Label(preview_settings_frame, text="预览文本:").grid(row=0, column=0, padx=(0, 5))
        self.preview_text_var = tk.StringVar(value="字体预览 Font Preview 123")
        preview_entry = ttk.Entry(preview_settings_frame, textvariable=self.preview_text_var)
        preview_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        preview_entry.bind('<Return>', self.update_preview)
        
        ttk.Button(preview_settings_frame, text="更新预览", command=self.update_preview).grid(row=0, column=2)
        
        # 字体预览区域
        preview_frame = ttk.Frame(right_frame)
        preview_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        
        # 预览画布
        self.preview_canvas = tk.Canvas(preview_frame, bg='white', height=200)
        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 字体信息
        info_frame = ttk.LabelFrame(right_frame, text="字体信息", padding="5")
        info_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        self.font_info_text = tk.Text(info_frame, height=4, wrap=tk.WORD, font=("微软雅黑", 10))
        self.font_info_text.pack(fill=tk.BOTH, expand=True)
        
        # 设置按钮
        settings_frame = ttk.Frame(right_frame)
        settings_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Button(settings_frame, text="设为默认字体", command=self.set_as_default).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(settings_frame, text="应用", command=self.apply_font).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(settings_frame, text="关闭", command=self.close_window).pack(side=tk.RIGHT)
        
        # 存储字体列表
        self.all_fonts = {}
        self.filtered_fonts = {}
        self.current_font = None
    
    def load_fonts(self):
        """加载字体列表"""
        def load_in_background():
            try:
                # 扫描系统字体
                self.font_manager.scan_system_fonts()
                
                # 获取所有字体
                self.all_fonts = self.font_manager.get_all_fonts()
                self.filtered_fonts = self.all_fonts.copy()
                
                # 在主线程中更新UI
                self.window.after(0, self.update_font_list)
                
            except Exception as e:
                self.window.after(0, lambda: messagebox.showerror("错误", f"加载字体失败: {e}"))
        
        # 显示加载提示
        self.font_listbox.delete(0, tk.END)
        self.font_listbox.insert(0, "正在加载字体...")
        
        # 在后台线程中加载字体
        threading.Thread(target=load_in_background, daemon=True).start()
    
    def update_font_list(self):
        """更新字体列表显示"""
        self.font_listbox.delete(0, tk.END)
        
        for font_name in sorted(self.filtered_fonts.keys()):
            self.font_listbox.insert(tk.END, font_name)
        
        # 选择默认字体或最后使用的字体
        default_font = self.font_manager.get_default_font()
        last_used_font = self.font_manager.get_last_used_font()
        
        target_font = default_font or last_used_font
        if target_font:
            # 查找匹配的字体
            for i, font_name in enumerate(sorted(self.filtered_fonts.keys())):
                if target_font in font_name:
                    self.font_listbox.selection_set(i)
                    self.font_listbox.see(i)
                    self.on_font_selected(None)
                    break
    
    def on_search_changed(self, *args):
        """搜索框内容变化时的处理"""
        search_text = self.search_var.get().lower()
        
        if search_text:
            self.filtered_fonts = {
                name: path for name, path in self.all_fonts.items()
                if search_text in name.lower()
            }
        else:
            self.filtered_fonts = self.all_fonts.copy()
        
        self.update_font_list()
    
    def on_font_selected(self, event):
        """字体选择变化时的处理"""
        selection = self.font_listbox.curselection()
        if selection:
            font_name = self.font_listbox.get(selection[0])
            self.current_font = font_name
            self.show_font_info(font_name)
            self.update_preview()
    
    def show_font_info(self, font_name):
        """显示字体信息"""
        self.font_info_text.delete(1.0, tk.END)
        
        font_path = self.font_manager.get_font_path(font_name)
        if font_path:
            info = f"字体名称: {font_name}\n"
            info += f"文件路径: {font_path}\n"
            
            if os.path.exists(font_path):
                file_size = os.path.getsize(font_path)
                info += f"文件大小: {file_size / 1024:.1f} KB\n"
            
            if "[自定义]" in font_name:
                info += "类型: 自定义字体"
            else:
                info += "类型: 系统字体"
            
            self.font_info_text.insert(1.0, info)
    
    def update_preview(self, event=None):
        """更新字体预览"""
        if not self.current_font:
            return
        
        try:
            preview_text = self.preview_text_var.get()
            preview_img = self.font_manager.create_font_preview(
                self.font_manager.get_font_path(self.current_font),
                preview_text,
                size=(400, 100)
            )
            
            if preview_img:
                # 转换为PhotoImage
                photo = ImageTk.PhotoImage(preview_img)
                
                # 清除画布并显示预览
                self.preview_canvas.delete("all")
                self.preview_canvas.create_image(200, 50, image=photo)
                
                # 保持引用防止被垃圾回收
                self.preview_canvas.image = photo
        
        except Exception as e:
            print(f"更新预览失败: {e}")
    
    def refresh_fonts(self):
        """刷新字体列表"""
        self.load_fonts()
    
    def add_font(self):
        """添加自定义字体"""
        file_path = filedialog.askopenfilename(
            title="选择字体文件",
            filetypes=[
                ("字体文件", "*.ttf *.otf *.ttc"),
                ("TrueType字体", "*.ttf"),
                ("OpenType字体", "*.otf"),
                ("TrueType集合", "*.ttc"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            # 询问字体名称
            font_name = tk.simpledialog.askstring(
                "字体名称",
                "请输入字体显示名称:",
                initialvalue=os.path.splitext(os.path.basename(file_path))[0]
            )
            
            if font_name:
                if self.font_manager.add_custom_font(file_path, font_name):
                    messagebox.showinfo("成功", f"成功添加字体: {font_name}")
                    self.load_fonts()
                else:
                    messagebox.showerror("失败", "添加字体失败")
    
    def remove_font(self):
        """删除自定义字体"""
        if not self.current_font or "[自定义]" not in self.current_font:
            messagebox.showwarning("提示", "请选择一个自定义字体进行删除")
            return
        
        if messagebox.askyesno("确认删除", f"确定要删除字体 {self.current_font} 吗？"):
            clean_name = self.current_font.replace("[自定义] ", "")
            if self.font_manager.remove_custom_font(clean_name):
                messagebox.showinfo("成功", "字体删除成功")
                self.load_fonts()
            else:
                messagebox.showerror("失败", "删除字体失败")
    
    def set_as_default(self):
        """设置为默认字体"""
        if self.current_font:
            self.font_manager.set_default_font(self.current_font)
            messagebox.showinfo("成功", f"已将 {self.current_font} 设为默认字体")
    
    def apply_font(self):
        """应用字体（关闭窗口并返回选择的字体）"""
        if self.current_font:
            self.font_manager.set_last_used_font(self.current_font)
            # 可以通过回调函数通知父窗口字体选择
            if hasattr(self, 'on_font_applied'):
                self.on_font_applied(self.current_font)
        self.close_window()
    
    def close_window(self):
        """关闭窗口"""
        self.window.destroy()


# 简化的字体选择对话框
def show_font_dialog(parent, font_manager, current_font=None):
    """
    显示字体选择对话框
    
    参数:
        parent: 父窗口
        font_manager: 字体管理器
        current_font: 当前选择的字体
    
    返回:
        str: 选择的字体名称，如果取消则返回None
    """
    result = [None]
    
    def on_font_applied(font_name):
        result[0] = font_name
    
    window = FontManagerWindow(parent, font_manager)
    window.on_font_applied = on_font_applied
    
    # 如果有当前字体，选中它
    if current_font:
        # 在字体列表中查找并选中
        for i in range(window.font_listbox.size()):
            if current_font in window.font_listbox.get(i):
                window.font_listbox.selection_set(i)
                window.font_listbox.see(i)
                window.on_font_selected(None)
                break
    
    # 等待窗口关闭
    parent.wait_window(window.window)
    
    return result[0]
